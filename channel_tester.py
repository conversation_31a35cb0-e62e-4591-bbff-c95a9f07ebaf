import requests
import random
import concurrent.futures
import m3u8
import socket  # Used in _test_non_http_stream method
import cloudscraper25  # Enhanced version for bypassing Cloudflare protection
import os
import json
from urllib.parse import urlparse, parse_qs

def create_browsers_json_if_missing():
    """Create browsers.json file if it's missing (for .exe builds)"""
    try:
        import cloudscraper25
        cloudscraper_path = os.path.dirname(cloudscraper25.__file__)
        user_agent_path = os.path.join(cloudscraper_path, 'user_agent')
        browsers_json_path = os.path.join(user_agent_path, 'browsers.json')

        # Check if browsers.json exists
        if not os.path.exists(browsers_json_path):
            # Create the user_agent directory if it doesn't exist
            os.makedirs(user_agent_path, exist_ok=True)

            # Create a comprehensive browsers.json file with proper structure
            browsers_data = {
                "user_agents": {
                    "chrome": [
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36"
                    ],
                    "firefox": [
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0",
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:118.0) Gecko/20100101 Firefox/118.0"
                    ]
                },
                "headers": {
                    "chrome": {
                        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                        "Accept-Language": "en-US,en;q=0.5",
                        "Accept-Encoding": "gzip, deflate",
                        "Connection": "keep-alive",
                        "Upgrade-Insecure-Requests": "1"
                    },
                    "firefox": {
                        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                        "Accept-Language": "en-US,en;q=0.5",
                        "Accept-Encoding": "gzip, deflate",
                        "Connection": "keep-alive",
                        "Upgrade-Insecure-Requests": "1"
                    }
                }
            }

            # Write the browsers.json file
            with open(browsers_json_path, 'w', encoding='utf-8') as f:
                json.dump(browsers_data, f, indent=2)

            return True
    except Exception as e:
        print(f"Error creating browsers.json: {e}")
        return False

    return False

class ChannelTester:
    def __init__(self, timeout=8, use_cloudscraper=False):
        """
        Initialize the channel tester

        Args:
            timeout (int): Maximum time in seconds to wait for a channel to load
            use_cloudscraper (bool): Whether to use cloudscraper25 for Cloudflare bypass
        """
        self.timeout = timeout
        self.use_cloudscraper = use_cloudscraper

        # Regular requests session
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'VLC/3.0.18 LibVLC/3.0.18',
            'Connection': 'keep-alive',
            'Icy-MetaData': '1'
        })

        # Cloudscraper25 session with enhanced v3 JavaScript VM and Turnstile support
        try:
            self.cloudscraper_session = cloudscraper25.create_scraper(
                browser={
                    'browser': 'chrome',
                    'platform': 'windows',
                    'desktop': True
                },
                # Enhanced JavaScript interpreter for v3 VM challenges
                interpreter='js2py',  # Best compatibility for v3 challenges

                # Enable all challenge types including v3 and Turnstile
                disableCloudflareV1=False,  # Keep v1 support for legacy sites
                disableCloudflareV2=False,  # Keep v2 support for standard sites
                disableCloudflareV3=False,  # Enable v3 JavaScript VM challenges
                disableTurnstile=False,     # Enable Turnstile CAPTCHA support

                # Enhanced stealth mode for v3 detection avoidance
                enable_stealth=True,
                stealth_options={
                    'min_delay': 2.0,           # Longer delays for v3 challenges
                    'max_delay': 6.0,           # Extended max delay for complex challenges
                    'human_like_delays': True,   # Simulate human behavior
                    'randomize_headers': True,   # Avoid fingerprinting
                    'browser_quirks': True       # Apply browser-specific behaviors
                },

                # Extended delay for complex v3 challenges
                delay=5,  # Allow more time for JavaScript VM execution

                # Enable debug mode for troubleshooting (can be disabled in production)
                debug=False
            )
        except (FileNotFoundError, KeyError) as e:
            if "browsers.json" in str(e) or "user_agents" in str(e):
                # Try to create the missing browsers.json file
                print("browsers.json or user_agents missing, attempting to create it...")

                if create_browsers_json_if_missing():
                    print("Created browsers.json file, retrying cloudscraper25...")
                    # Retry creating the cloudscraper session
                    try:
                        self.cloudscraper_session = cloudscraper25.create_scraper(
                            browser={'browser': 'chrome', 'platform': 'windows', 'desktop': True},
                            interpreter='js2py',
                            disableCloudflareV1=False,
                            disableCloudflareV2=False,
                            disableCloudflareV3=False,
                            disableTurnstile=False,
                            enable_stealth=True,
                            stealth_options={
                                'min_delay': 2.0,
                                'max_delay': 6.0,
                                'human_like_delays': True,
                                'randomize_headers': True,
                                'browser_quirks': True
                            },
                            delay=5,
                            debug=False
                        )
                    except Exception as retry_error:
                        print(f"Retry failed: {str(retry_error)}, using fallback session")
                        self.cloudscraper_session = None
                else:
                    print("Failed to create browsers.json, using fallback session")
                    self.cloudscraper_session = None
            else:
                print(f"Cloudscraper25 initialization failed: {str(e)}")
                self.cloudscraper_session = None
        except Exception as e:
            print(f"Cloudscraper25 initialization failed: {str(e)}")
            self.cloudscraper_session = None
        # Update cloudscraper headers to match our regular session (if available)
        if self.cloudscraper_session:
            self.cloudscraper_session.headers.update({
                'Connection': 'keep-alive',
                'Icy-MetaData': '1'
            })

    def get_random_user_agent(self):
        """Get a random user agent string that mimics VLC"""
        vlc_versions = ['3.0.16', '3.0.17', '3.0.18', '3.0.19']
        version = random.choice(vlc_versions)
        return f"VLC/{version} LibVLC/{version}"

    def _is_cloudflare_protected(self, response):
        """
        Check if a response indicates Cloudflare protection

        Args:
            response: The response object to check

        Returns:
            bool: True if the response indicates Cloudflare protection
        """
        # Check for common Cloudflare indicators
        if response.status_code == 403 or response.status_code == 503:
            # Check for Cloudflare specific headers or content
            cf_ray = response.headers.get('CF-RAY')
            if cf_ray:
                return True

            # Check for Cloudflare challenge page content
            if 'challenge-platform' in response.text or 'cloudflare' in response.text.lower():
                return True

            # Check for specific Cloudflare strings
            if 'checking your browser' in response.text.lower() or 'security check' in response.text.lower():
                return True

        return False

    def _detect_challenge_type(self, response):
        """
        Detect the type of Cloudflare challenge present (v3 JavaScript VM support)

        Args:
            response: The HTTP response object

        Returns:
            str: Challenge type ('v1', 'v2', 'v3', 'turnstile', 'none')
        """
        if not response or not hasattr(response, 'text'):
            return 'none'

        content = response.text.lower()

        # Check for v3 JavaScript VM challenges (newest and most sophisticated)
        v3_indicators = [
            'cf_chl_opt',           # v3 challenge options
            'cf_chl_prog',          # v3 challenge progress
            'cf_chl_seq',           # v3 challenge sequence
            'window._cf_chl_opt',   # v3 window object
            'cpo.src',              # v3 challenge script source
            'cf_chl_rt_',           # v3 runtime token
            'cf-challenge-running', # v3 challenge status
            'cf_chl_tk',            # v3 challenge token
            'cf_chl_captcha_tk'     # v3 captcha token
        ]

        if any(indicator in content for indicator in v3_indicators):
            print(f"Detected Cloudflare v3 JavaScript VM challenge")
            return 'v3'

        # Check for Turnstile challenges (Cloudflare's CAPTCHA replacement)
        turnstile_indicators = [
            'cf-turnstile',                    # Turnstile widget
            'turnstile.js',                    # Turnstile script
            'cf-turnstile-response',           # Turnstile response
            'data-sitekey',                    # Turnstile site key
            'challenges.cloudflare.com/turnstile', # Turnstile endpoint
            'turnstile-wrapper',               # Turnstile container
            'cf-turnstile-container'           # Turnstile container
        ]

        if any(indicator in content for indicator in turnstile_indicators):
            print(f"Detected Cloudflare Turnstile challenge")
            return 'turnstile'

        # Check for v2 challenges (standard JavaScript challenges)
        v2_indicators = [
            'cf_chl_jschl_tk',     # v2 JavaScript challenge token
            'cf_chl_captcha_tk',   # v2 CAPTCHA token
            'cf_chl_2',            # v2 challenge identifier
            'jschl_vc',            # v2 verification code
            'jschl_answer',        # v2 challenge answer
            'cf_chl_managed_tk'    # v2 managed token
        ]

        if any(indicator in content for indicator in v2_indicators):
            print(f"Detected Cloudflare v2 challenge")
            return 'v2'

        # Check for v1 challenges (legacy)
        v1_indicators = [
            'settimeout',          # v1 timeout function
            'challenge-form',      # v1 challenge form
            'cf_chl_jschl_tk',    # v1 JavaScript token
            'cf_chl_prog'         # v1 progress indicator
        ]

        if any(indicator in content for indicator in v1_indicators):
            print(f"Detected Cloudflare v1 challenge")
            return 'v1'

        return 'none'

    def _test_non_http_stream(self, stream_url):
        """
        Test non-HTTP streams (like RTMP) by checking if the server responds

        Args:
            stream_url (str): The URL of the non-HTTP stream to test

        Returns:
            bool: True if the server responds, False otherwise
        """
        try:
            # Parse the URL to get host and port
            parsed_url = urlparse(stream_url)
            host = parsed_url.hostname

            # If we can't determine the host, consider it not working
            if not host:
                return False

            # For non-HTTP protocols, we'll just check if the server responds to a ping
            # This is a very basic test and doesn't guarantee the stream works
            port = parsed_url.port or 1935  # Default RTMP port is 1935

            # Use socket to test connection
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            result = sock.connect_ex((host, port))
            sock.close()

            return result == 0  # 0 means connection successful

        except Exception as e:
            print(f"Error testing non-HTTP stream {stream_url}: {e}")
            return False

    def test_channel(self, stream_url, mac=None, base_url=None):
        """
        Test if a channel stream works using VLC-style validation

        Args:
            stream_url (str): The URL of the channel stream to test
            mac (str, optional): MAC address for authentication
            base_url (str, optional): Base URL of the portal

        Returns:
            bool: True if the channel works, False otherwise
        """
        try:
            # Handle URLs that start with "ffmpeg" by removing the prefix
            if stream_url.startswith("ffmpeg "):
                stream_url = stream_url.replace("ffmpeg ", "", 1)

            # If we have a base_url and mac, try to convert to a direct play URL
            if base_url and mac and 'cmd=' in stream_url:
                parsed = urlparse(stream_url)
                query = parse_qs(parsed.query)
                stream_id = query.get('stream', [''])[0]
                if stream_id:
                    stream_url = (
                        f"{base_url}/play/live.php?"
                        f"mac={mac}&"
                        f"stream={stream_id}&"
                        f"extension=ts"
                    )

            # Extract stream ID from play URL if available
            if 'play/live.php' in stream_url and 'stream=' in stream_url and not stream_url.startswith('http'):
                # If URL doesn't start with http, add the base_url
                if base_url:
                    stream_url = f"{base_url}/{stream_url.lstrip('/')}"

            # Parse the URL to handle different protocols
            parsed_url = urlparse(stream_url)

            # Skip non-HTTP URLs (like rtmp://)
            if parsed_url.scheme not in ['http', 'https']:
                # For non-HTTP protocols, we'll do a simple connection test
                return self._test_non_http_stream(stream_url)

            # Set up headers with a VLC-like user agent
            headers = {
                'User-Agent': self.get_random_user_agent(),
                'Range': 'bytes=0-512000',
                'Connection': 'keep-alive',
                'Icy-MetaData': '1'
            }

            # First try with regular session
            try:
                # Make a GET request with streaming enabled
                response = self.session.get(
                    stream_url,
                    headers=headers,
                    timeout=self.timeout,
                    stream=True,
                    allow_redirects=True
                )

                # Check if the response is protected by Cloudflare with enhanced detection
                if self.use_cloudscraper and self.cloudscraper_session and self._is_cloudflare_protected(response):
                    challenge_type = self._detect_challenge_type(response)
                    print(f"Cloudflare {challenge_type} challenge detected for {stream_url}, using enhanced cloudscraper25")

                    # Try again with cloudscraper25 with extended timeout for v3 challenges
                    timeout_multiplier = 3 if challenge_type == 'v3' else 2
                    response = self.cloudscraper_session.get(
                        stream_url,
                        headers=headers,
                        timeout=self.timeout * timeout_multiplier,  # Extra time for v3 VM challenges
                        stream=True,
                        allow_redirects=True
                    )
            except Exception as e:
                if self.use_cloudscraper and self.cloudscraper_session:
                    # If regular session fails, try with cloudscraper25
                    print(f"Regular session failed for {stream_url}, trying cloudscraper25: {e}")
                    try:
                        response = self.cloudscraper_session.get(
                            stream_url,
                            headers=headers,
                            timeout=self.timeout * 2,  # Give more time for Cloudflare bypass
                            stream=True,
                            allow_redirects=True
                        )
                    except Exception as e2:
                        print(f"Cloudscraper25 also failed for {stream_url}: {e2}")
                        return False
                else:
                    # If not using cloudscraper25 or it's not available, just fail
                    return False

            # Check if the response status code indicates success
            if response.status_code not in [200, 206]:
                return False

            # Check content type to ensure it's a valid stream format
            content_type = response.headers.get('Content-Type', '').lower()
            valid_types = [
                'video/mp2t', 'application/vnd.apple.mpegurl',
                'application/x-mpegurl', 'video/mpeg',
                'video/mp4', 'application/octet-stream'
            ]

            if not any(t in content_type for t in valid_types):
                return False

            # Read a small chunk of data to verify the stream
            bytes_received = 0
            for chunk in response.iter_content(4096):
                bytes_received += len(chunk)
                if bytes_received >= 512000:  # Stop after receiving 512KB
                    break

            # For HLS streams, try to parse the playlist
            if 'mpegurl' in content_type:
                try:
                    playlist = m3u8.load(stream_url)
                    return bool(playlist.segments)
                except Exception:
                    pass

            # If we received data, consider the stream working
            return bytes_received > 0

        except Exception as e:
            print(f"Error testing channel {stream_url}: {e}")
            return False

    def check_supported_formats(self, base_url):
        """
        Check which stream formats are supported by the server

        Args:
            base_url (str): Base URL of the portal

        Returns:
            list: List of supported format names
        """
        formats = {
            'HLS': ('m3u8', 'application/vnd.apple.mpegurl'),
            'MPEG-TS': ('ts', 'video/mp2t'),
            'MP4': ('mp4', 'video/mp4')
        }

        supported = []
        for fmt, (ext, mime) in formats.items():
            try:
                # First try with regular session
                headers = {'User-Agent': self.get_random_user_agent()}
                url = f"{base_url}/play/test.{ext}"

                try:
                    response = self.session.head(
                        url,
                        timeout=self.timeout,
                        headers=headers
                    )

                    # Check if the response is protected by Cloudflare
                    if self.use_cloudscraper and self.cloudscraper_session and response.status_code in [403, 503]:
                        # Try a GET request to check for Cloudflare
                        check_response = self.session.get(
                            url,
                            timeout=self.timeout,
                            headers=headers
                        )

                        if self._is_cloudflare_protected(check_response):
                            print(f"Cloudflare protection detected for format check {url}, using cloudscraper25")
                            # Try again with cloudscraper25
                            response = self.cloudscraper_session.head(
                                url,
                                timeout=self.timeout * 2,
                                headers=headers
                            )
                except Exception as e:
                    if self.use_cloudscraper and self.cloudscraper_session:
                        # If regular session fails, try with cloudscraper25
                        print(f"Regular session failed for format check {url}, trying cloudscraper25: {e}")
                        response = self.cloudscraper_session.head(
                            url,
                            timeout=self.timeout * 2,
                            headers=headers
                        )
                    else:
                        # If not using cloudscraper25 or it's not available, just continue to next format
                        continue

                if response.status_code == 200:
                    content_type = response.headers.get('Content-Type', '').lower()
                    if mime in content_type:
                        supported.append(fmt)
            except Exception as e:
                print(f"Error checking format {fmt}: {e}")
                continue
        return supported

    def test_multiple_channels(self, channel_info, max_channels=5, mac=None, base_url=None):
        """
        Test multiple channel streams in parallel and return results with quality assessment

        Args:
            channel_info (dict): Dictionary with channel information {url: {'name': name, 'group': group}}
            max_channels (int): Maximum number of channels to test
            mac (str, optional): MAC address for authentication
            base_url (str, optional): Base URL of the portal

        Returns:
            dict: Dictionary with results and quality assessment
        """
        if not channel_info:
            return {'channels': {}, 'quality': 'Dead', 'working_count': 0, 'total': 0}

        # Limit the number of channels to test
        urls = list(channel_info.keys())
        sample_size = min(max_channels, len(urls))
        sample_urls = random.sample(urls, sample_size)

        # Test channels in parallel using ThreadPoolExecutor
        results = {}
        with concurrent.futures.ThreadPoolExecutor(max_workers=min(5, sample_size)) as executor:
            future_to_url = {
                executor.submit(self.test_channel, url, mac, base_url): url
                for url in sample_urls
            }

            for future in concurrent.futures.as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    is_working = future.result()
                    info = channel_info.get(url, {'name': 'Unknown', 'group': 'Unknown'})
                    results[url] = {
                        'name': info['name'],
                        'group': info['group'],
                        'working': is_working
                    }
                except Exception as e:
                    print(f"Error processing result for {url}: {e}")
                    results[url] = {
                        'name': channel_info.get(url, {}).get('name', 'Unknown'),
                        'group': channel_info.get(url, {}).get('group', 'Unknown'),
                        'working': False
                    }

        # Calculate working channels and determine quality
        working_count = sum(1 for info in results.values() if info['working'])
        total_tested = len(results)

        # Determine connection quality based on working channels
        quality = "Dead"
        if working_count >= 5:
            quality = "Excellent"
        elif working_count >= 4:
            quality = "Good"
        elif working_count >= 2:
            quality = "Fair"
        elif working_count >= 1:
            quality = "Poor"

        return {
            'channels': results,
            'quality': quality,
            'working_count': working_count,
            'total': total_tested
        }
