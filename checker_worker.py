from PyQt6.QtCore import QThread, pyqtSignal, QMutex, QThreadPool, QRunnable
import time
import queue
import cloudscraper25  # Import cloudscraper25 for enhanced Cloudflare bypass
import random
import os
import json
from mac_generator import generate_mac_combinations, read_mac_file

def create_browsers_json_if_missing():
    """Create browsers.json file if it's missing (for .exe builds)"""
    try:
        import cloudscraper25
        cloudscraper_path = os.path.dirname(cloudscraper25.__file__)
        user_agent_path = os.path.join(cloudscraper_path, 'user_agent')
        browsers_json_path = os.path.join(user_agent_path, 'browsers.json')

        # Check if browsers.json exists
        if not os.path.exists(browsers_json_path):
            # Create the user_agent directory if it doesn't exist
            os.makedirs(user_agent_path, exist_ok=True)

            # Create a comprehensive browsers.json file with proper structure
            browsers_data = {
                "user_agents": {
                    "chrome": [
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36"
                    ],
                    "firefox": [
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0",
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:118.0) Gecko/20100101 Firefox/118.0"
                    ]
                },
                "headers": {
                    "chrome": {
                        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                        "Accept-Language": "en-US,en;q=0.5",
                        "Accept-Encoding": "gzip, deflate",
                        "Connection": "keep-alive",
                        "Upgrade-Insecure-Requests": "1"
                    },
                    "firefox": {
                        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                        "Accept-Language": "en-US,en;q=0.5",
                        "Accept-Encoding": "gzip, deflate",
                        "Connection": "keep-alive",
                        "Upgrade-Insecure-Requests": "1"
                    }
                }
            }

            # Write the browsers.json file
            with open(browsers_json_path, 'w', encoding='utf-8') as f:
                json.dump(browsers_data, f, indent=2)

            return True
    except Exception as e:
        print(f"Error creating browsers.json: {e}")
        return False

    return False

class CheckerTask(QRunnable):
    def __init__(self, mac_queue, base_url, use_proxies, checker_config, callback, cloudscraper_session=None):
        super().__init__()
        self.mac_queue = mac_queue
        self.base_url = base_url
        self.use_proxies = use_proxies
        self.checker_config = checker_config
        self.callback = callback
        self.cloudscraper_session = cloudscraper_session  # Use the provided cloudscraper25 session

    def run(self):
        from checker import Checker
        checker = Checker()

        # Configure the checker with enhanced settings
        checker.configure(**self.checker_config)

        # If a cloudscraper25 session was provided, use it
        if self.cloudscraper_session and self.checker_config.get('use_cloudscraper', True):
            # Add the cloudscraper25 session to the session pool
            checker.session_pool.append(self.cloudscraper_session)

        while True:
            try:
                mac = self.mac_queue.get_nowait()
                try:
                    result = checker.check_mac(mac, self.base_url, self.use_proxies)
                    self.callback(result, mac)
                except Exception as e:
                    self.callback(None, f"{mac} | Error: {str(e)}")
                self.mac_queue.task_done()
            except queue.Empty:
                break

class CheckerWorker(QThread):
    update_signal = pyqtSignal(str, str, int)
    progress_signal = pyqtSignal(int, int)

    def __init__(self, base_url, mac_source, mac_param, use_proxies, num_threads, checker_config):
        super().__init__()
        self.base_url = base_url
        self.mac_source = mac_source
        self.mac_param = mac_param
        self.use_proxies = use_proxies
        self.num_threads = num_threads
        self.checker_config = checker_config
        self.stop_flag = False
        self.hits = 0
        self.tries = 0
        self.mutex = QMutex()
        self.pool = QThreadPool()
        self.mac_queue = queue.Queue()

        # Create cloudscraper25 sessions with enhanced features if enabled
        self.cloudscraper_sessions = []
        if checker_config.get('use_cloudscraper', True):
            self.create_cloudscraper_sessions(num_threads)

    def create_cloudscraper_sessions(self, num_sessions):
        """Create cloudscraper25 sessions with enhanced features"""
        self.update_signal.emit(
            "Creating cloudscraper25 sessions with enhanced features...",
            "info",
            1
        )

        # Browser configurations to randomize
        browsers = [
            {'browser': 'chrome', 'platform': 'windows', 'desktop': True},
            {'browser': 'firefox', 'platform': 'windows', 'desktop': True},
            {'browser': 'chrome', 'platform': 'darwin', 'desktop': True},
            {'browser': 'firefox', 'platform': 'darwin', 'desktop': True}
        ]

        for i in range(num_sessions):
            # Randomize browser configuration
            browser_config = random.choice(browsers)

            try:
                # Create a cloudscraper25 session with v3 JavaScript VM and Turnstile support
                session = cloudscraper25.create_scraper(
                    browser=browser_config,

                    # Enhanced JavaScript interpreter for v3 VM challenges
                    interpreter='js2py',  # Best compatibility for v3 challenges

                    # Enable all challenge types including v3 and Turnstile
                    disableCloudflareV1=False,  # Keep v1 support for legacy sites
                    disableCloudflareV2=False,  # Keep v2 support for standard sites
                    disableCloudflareV3=False,  # Enable v3 JavaScript VM challenges
                    disableTurnstile=False,     # Enable Turnstile CAPTCHA support

                    # Enhanced stealth mode for v3 detection avoidance
                    enable_stealth=True,
                    stealth_options={
                        'min_delay': 2.0,           # Longer delays for v3 challenges
                        'max_delay': 6.0,           # Extended max delay for complex challenges
                        'human_like_delays': True,   # Simulate human behavior
                        'randomize_headers': True,   # Avoid fingerprinting
                        'browser_quirks': True       # Apply browser-specific behaviors
                    },

                    # Extended delay for complex v3 challenges
                    delay=5,  # Allow more time for JavaScript VM execution

                    # Enable debug mode for troubleshooting (can be disabled in production)
                    debug=False
                )
            except FileNotFoundError as e:
                if "browsers.json" in str(e):
                    # Try to create the missing browsers.json file
                    self.update_signal.emit(
                        f"browsers.json not found, attempting to create it...",
                        "warning",
                        0
                    )

                    if create_browsers_json_if_missing():
                        self.update_signal.emit(
                            f"Created browsers.json file, retrying cloudscraper25...",
                            "info",
                            0
                        )
                        # Retry creating the cloudscraper session
                        try:
                            session = cloudscraper25.create_scraper(
                                browser=browser_config,
                                interpreter='js2py',
                                disableCloudflareV1=False,
                                disableCloudflareV2=False,
                                disableCloudflareV3=False,
                                disableTurnstile=False,
                                enable_stealth=True,
                                stealth_options={
                                    'min_delay': 2.0,
                                    'max_delay': 6.0,
                                    'human_like_delays': True,
                                    'randomize_headers': True,
                                    'browser_quirks': True
                                },
                                delay=5,
                                debug=False
                            )
                        except Exception as retry_error:
                            self.update_signal.emit(
                                f"Retry failed: {str(retry_error)}, using fallback session",
                                "warning",
                                0
                            )
                            # Create a basic requests session as fallback
                            import requests
                            session = requests.Session()
                            session.headers.update({
                                'User-Agent': browser_config.get('User-Agent',
                                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'),
                                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                                'Accept-Language': 'en-US,en;q=0.5',
                                'Accept-Encoding': 'gzip, deflate',
                                'Connection': 'keep-alive',
                                'Upgrade-Insecure-Requests': '1',
                            })
                    else:
                        # Failed to create browsers.json, use fallback
                        self.update_signal.emit(
                            f"Failed to create browsers.json, using fallback session",
                            "warning",
                            0
                        )
                        import requests
                        session = requests.Session()
                        session.headers.update({
                            'User-Agent': browser_config.get('User-Agent',
                                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'),
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                            'Accept-Language': 'en-US,en;q=0.5',
                            'Accept-Encoding': 'gzip, deflate',
                            'Connection': 'keep-alive',
                            'Upgrade-Insecure-Requests': '1',
                        })
                else:
                    raise e
            except Exception as e:
                # Handle other cloudscraper errors
                self.update_signal.emit(
                    f"Warning: cloudscraper25 failed ({str(e)}), using basic session",
                    "warning",
                    0
                )
                # Create a basic requests session as fallback
                import requests
                session = requests.Session()

                # Add basic headers to mimic a browser
                session.headers.update({
                    'User-Agent': browser_config.get('User-Agent',
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'),
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                })

            # Add proxy rotation if enabled
            if self.use_proxies:
                try:
                    # Get proxies from the proxy handler
                    from proxy_handler import ProxyHandler
                    proxy_handler = ProxyHandler()

                    # If proxies are available, configure cloudscraper25 to use them
                    if proxy_handler.proxies:
                        # Convert proxies to the format expected by cloudscraper25
                        rotating_proxies = []
                        for proxy in proxy_handler.proxies:
                            if isinstance(proxy, dict):
                                # Already in the right format
                                rotating_proxies.append(proxy)
                            elif isinstance(proxy, str):
                                # Convert string format to dict format
                                rotating_proxies.append({
                                    'http': proxy,
                                    'https': proxy
                                })

                        # Configure session with rotating proxies if available
                        if rotating_proxies and hasattr(session, 'configure'):
                            session.configure(
                                rotating_proxies=rotating_proxies,
                                proxy_options={
                                    'rotation_strategy': 'smart',
                                    'ban_time': 300  # Ban failing proxies for 5 minutes
                                }
                            )
                            self.update_signal.emit(
                                f"Configured cloudscraper25 session with {len(rotating_proxies)} proxies",
                                "info",
                                1
                            )
                except Exception as e:
                    self.update_signal.emit(
                        f"Error configuring proxy rotation: {str(e)}",
                        "error",
                        0
                    )

            self.cloudscraper_sessions.append(session)

        self.update_signal.emit(
            f"Created {len(self.cloudscraper_sessions)} cloudscraper25 sessions",
            "info",
            1
        )

    def run(self):
        # Fill the MAC queue with initial batch
        try:
            # Create the MAC generator based on source
            if self.mac_source == 0:  # Generate MACs
                mac_generator = generate_mac_combinations(start_from=self.mac_param)
                self.update_signal.emit(
                    f"Starting MAC generation from: {self.mac_param}",
                    "info",
                    1
                )
            else:  # Read from file
                mac_generator = read_mac_file(self.mac_param)
                self.update_signal.emit(
                    f"Reading MACs from file: {self.mac_param}",
                    "info",
                    1
                )

            # Add initial batch of MACs to the queue
            batch_size = self.num_threads * 10  # Pre-fill with 10x thread count
            mac_count = 0

            for mac in mac_generator:
                if self.stop_flag:
                    break
                self.mac_queue.put(mac)
                mac_count += 1

                # Log progress for large batches
                if mac_count % 1000 == 0:
                    self.update_signal.emit(
                        f"Added {mac_count} MACs to queue...",
                        "info",
                        1
                    )

                # For generated MACs, only add a batch at a time to avoid memory issues
                if self.mac_source == 0 and mac_count >= batch_size:
                    break

            # Configure thread pool
            self.pool.setMaxThreadCount(self.num_threads)

            # Create worker tasks
            for i in range(self.num_threads):
                # Use cloudscraper25 session if available
                cloudscraper_session = None
                if self.checker_config.get('use_cloudscraper', True) and i < len(self.cloudscraper_sessions):
                    cloudscraper_session = self.cloudscraper_sessions[i]

                task = CheckerTask(
                    self.mac_queue,
                    self.base_url,
                    self.use_proxies,
                    self.checker_config,
                    self.handle_result,
                    cloudscraper_session
                )
                self.pool.start(task)

            # If generating MACs, continue adding more as the queue gets processed
            if self.mac_source == 0:
                while not self.stop_flag:
                    # If queue is getting low, add more MACs
                    if self.mac_queue.qsize() < self.num_threads * 2:
                        for _ in range(min(100, batch_size)):  # Add in smaller chunks
                            try:
                                mac = next(mac_generator)
                                self.mac_queue.put(mac)
                                mac_count += 1
                            except StopIteration:
                                # No more MACs to generate
                                self.update_signal.emit(
                                    f"Finished generating all possible MACs ({mac_count} total)",
                                    "info",
                                    1
                                )
                                break

                    # Small delay to prevent CPU hogging
                    time.sleep(0.1)

            # Wait for completion
            self.mac_queue.join()
            self.pool.waitForDone()

        except Exception as e:
            self.update_signal.emit(
                f"Error in worker: {str(e)}",
                "error",
                0
            )

    def handle_result(self, result, mac):
        self.mutex.lock()
        try:
            self.tries += 1
            if result:
                self.hits += 1
                mac, expiry, channels = result

                # Check if there's formats information available
                formats_info = ""
                if hasattr(result, 'supported_formats') and result.supported_formats:
                    formats_info = f"formats: {', '.join(result.supported_formats)}"

                self.update_signal.emit(
                    f"MAC: {mac}, Expiry: {expiry}, Channels: {channels}",
                    formats_info,
                    1
                )
            else:
                # Don't emit signal for invalid MACs to keep the UI clean
                # Just update the progress counter silently
                pass
            self.progress_signal.emit(self.hits, self.tries)
        finally:
            self.mutex.unlock()

    def stop(self):
        """Stop the worker (non-blocking)"""
        self.stop_flag = True

        # Clear the thread pool immediately
        self.pool.clear()

        # Clear the queue to unblock any waiting threads
        try:
            while not self.mac_queue.empty():
                try:
                    self.mac_queue.get_nowait()
                    self.mac_queue.task_done()
                except queue.Empty:
                    break
        except Exception:
            pass

        # Clean up cloudscraper sessions in a non-blocking way
        try:
            for session in self.cloudscraper_sessions:
                try:
                    if hasattr(session, 'close'):
                        session.close()
                except Exception:
                    pass
            self.cloudscraper_sessions.clear()
        except Exception:
            pass

        # Emit finished signal to notify GUI
        self.finished.emit()