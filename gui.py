#!/usr/bin/env python3
"""
Enhanced Checker Suite GUI with Tabbed Interface
Includes both Stalker Portal MAC Checker and Xtream Checker with v3 + Turnstile support
"""

from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
    QHBoxLayout, QGridLayout, QLabel, QLineEdit, QPushButton, QCheckBox, QTextEdit,
    QSpinBox, QFileDialog, QMessageBox, QFrame, QComboBox, QStackedWidget, QTabWidget)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QPixmap, QIcon
import sys
import time
import re
import os
import threading
from datetime import datetime
from checker_worker import CheckerWorker
from proxy_handler import ProxyHandler
from styles import Colors, set_button_style
from about_dialog import AboutDialog
from xtream_checker import format_server_name, get_formatted_filename
from xtream_combo_generator import XtreamComboGeneratorDialog
from xtream_worker import XtreamWorker
from player_module import PlayerWidget
from other_tools_module import OtherToolsWidget

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("MEGA IPTV TOOLS v1.0")
        self.setMinimumSize(1100, 750)
        icon = QIcon("logo1.png")
        self.setWindowIcon(icon)

        # Initialize proxy handler
        self.proxy_handler = ProxyHandler()

        # Initialize Stalker checker variables
        self.mac_file_path = ""
        self.stalker_worker = None
        self.stalker_check_count = 0

        # Initialize Xtream checker variables
        self.xtream_worker = None
        self.xtream_hit_count = 0
        self.xtream_bad_count = 0
        self.xtream_error_count = 0
        self.xtream_check_count = 0

        # Initialize CPM tracking
        self.cpm_timer = QTimer()
        self.cpm_timer.timeout.connect(self.update_cpm)
        self.last_tries = 0
        self.last_time = time.time()

        self.setup_ui()

    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Title and Logo
        title_layout = QHBoxLayout()
        logo_label = QLabel()
        pixmap = QPixmap("logo.png").scaled(32, 32, Qt.AspectRatioMode.KeepAspectRatio)
        logo_label.setPixmap(pixmap)
        title_layout.addWidget(logo_label)

        # Title
        title_label = QLabel("")
        title_label.setStyleSheet(f"color: {Colors.PRIMARY}; font-size: 16px; font-weight: bold;")
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        # About Button
        self.about_btn = QPushButton("About")
        set_button_style(self.about_btn, Colors.PRIMARY)
        self.about_btn.setStyleSheet("""
            QPushButton {
                padding: 4px 8px;
                font-size: 12px;
                min-width: 60px;
            }
        """)
        self.about_btn.clicked.connect(self.show_about_dialog)
        title_layout.addWidget(self.about_btn)
        layout.addLayout(title_layout)

        # Create tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {Colors.PRIMARY};
                background-color: {Colors.BACKGROUND};
            }}
            QTabBar::tab {{
                background-color: {Colors.SURFACE};
                color: {Colors.TEXT};
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                font-weight: bold;
            }}
            QTabBar::tab:selected {{
                background-color: {Colors.PRIMARY};
                color: white;
            }}
            QTabBar::tab:hover {{
                background-color: {Colors.PRIMARY}CC;
            }}
        """)

        # Create tabs
        self.setup_stalker_tab()
        self.setup_xtream_tab()
        self.setup_player_tab()
        self.setup_other_tools_tab()

        layout.addWidget(self.tab_widget)

    def setup_stalker_tab(self):
        """Setup the Stalker Portal MAC Checker tab"""
        stalker_widget = QWidget()
        layout = QVBoxLayout(stalker_widget)

        # URL Input
        url_layout = QHBoxLayout()
        url_label = QLabel("Portal URL:")
        url_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold;")
        self.stalker_url_input = QLineEdit()
        self.stalker_url_input.setStyleSheet(f"background-color: {Colors.SURFACE}; border: 1px solid #CCCCCC; border-radius: 4px; padding: 5px;")
        url_layout.addWidget(url_label)
        url_layout.addWidget(self.stalker_url_input)
        layout.addLayout(url_layout)

        # MAC Source
        mac_source_layout = QHBoxLayout()
        mac_source_label = QLabel("MAC Source:")
        mac_source_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold;")
        self.stalker_mac_source_combo = QComboBox()
        self.stalker_mac_source_combo.addItems(["Generate MACs", "Load from File"])
        self.stalker_mac_source_combo.currentIndexChanged.connect(self.toggle_stalker_mac_source)
        mac_source_layout.addWidget(mac_source_label)
        mac_source_layout.addWidget(self.stalker_mac_source_combo)
        layout.addLayout(mac_source_layout)

        # MAC Input Stack
        self.stalker_mac_input_stack = QStackedWidget()

        # Generate Panel
        generate_panel = QWidget()
        generate_layout = QHBoxLayout(generate_panel)
        generate_layout.addWidget(QLabel("Start MAC:"))
        generate_layout.addWidget(QLabel("00:1A:79:"))

        # MAC input with validation
        self.stalker_mac_input = QLineEdit()
        self.stalker_mac_input.setPlaceholderText("00:00:00")
        self.stalker_mac_input.setInputMask("HH:HH:HH")
        self.stalker_mac_input.setText("00:00:00")
        self.stalker_mac_input.setStyleSheet(f"""
            background-color: {Colors.SURFACE};
            border: 1px solid #CCCCCC;
            border-radius: 4px;
            padding: 5px;
            font-family: monospace;
        """)
        generate_layout.addWidget(self.stalker_mac_input)

        help_label = QLabel("(Format: XX:XX:XX in hex)")
        help_label.setStyleSheet(f"color: {Colors.TEXT}; font-style: italic;")
        generate_layout.addWidget(help_label)

        self.stalker_mac_input_stack.addWidget(generate_panel)

        # File Panel
        file_panel = QWidget()
        file_layout = QHBoxLayout(file_panel)
        self.stalker_mac_file_btn = QPushButton("Load MAC File")
        set_button_style(self.stalker_mac_file_btn, Colors.PRIMARY)
        self.stalker_mac_file_btn.clicked.connect(self.load_stalker_mac_file)
        self.stalker_mac_file_label = QLabel("No file selected")
        self.stalker_mac_file_label.setStyleSheet(f"color: {Colors.TEXT};")
        file_layout.addWidget(self.stalker_mac_file_btn)
        file_layout.addWidget(self.stalker_mac_file_label)
        self.stalker_mac_input_stack.addWidget(file_panel)

        layout.addWidget(self.stalker_mac_input_stack)

        # Threads Input
        thread_layout = QHBoxLayout()
        thread_label = QLabel("Threads:")
        thread_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold;")
        self.stalker_thread_input = QSpinBox()
        self.stalker_thread_input.setRange(1, 100)
        self.stalker_thread_input.setValue(10)
        self.stalker_thread_input.setFixedWidth(100)
        self.stalker_thread_input.setStyleSheet(f"""
            QSpinBox {{
                background-color: {Colors.SURFACE};
                border: 1px solid #CCCCCC;
                border-radius: 4px;
                padding: 5px 8px;
                min-width: 80px;
                font-size: 14px;
            }}
            QSpinBox::up-button {{ width: 25px; }}
            QSpinBox::down-button {{ width: 25px; }}
        """)
        thread_layout.addWidget(thread_label)
        thread_layout.addWidget(self.stalker_thread_input)
        thread_layout.addStretch()
        layout.addLayout(thread_layout)

        # Proxy Section
        proxy_layout = QHBoxLayout()
        self.stalker_use_proxies = QCheckBox("Use Proxies")
        self.stalker_use_proxies.setStyleSheet(f"color: {Colors.TEXT};")
        self.stalker_proxy_file_btn = QPushButton("Load Proxies")
        set_button_style(self.stalker_proxy_file_btn, Colors.PRIMARY)
        self.stalker_proxy_file_btn.clicked.connect(self.load_proxies)
        proxy_layout.addWidget(self.stalker_use_proxies)
        proxy_layout.addWidget(self.stalker_proxy_file_btn)
        layout.addLayout(proxy_layout)

        # Add the shared advanced settings
        self.add_shared_advanced_settings(layout)

        # Control Buttons
        button_layout = QHBoxLayout()
        self.stalker_start_btn = QPushButton("Start Stalker Check")
        self.stalker_stop_btn = QPushButton("Stop")
        set_button_style(self.stalker_start_btn, Colors.SUCCESS)
        set_button_style(self.stalker_stop_btn, Colors.ERROR)
        self.stalker_start_btn.clicked.connect(self.start_stalker_checking)
        self.stalker_stop_btn.clicked.connect(self.stop_stalker_checking)
        self.stalker_stop_btn.setEnabled(False)
        button_layout.addWidget(self.stalker_start_btn)
        button_layout.addWidget(self.stalker_stop_btn)
        layout.addLayout(button_layout)

        # Add Stalker-specific stats and log
        self.add_stalker_stats_and_log(layout)

        self.tab_widget.addTab(stalker_widget, "🔍 Stalker Portal Checker")

    def add_shared_advanced_settings(self, layout):
        """Add shared advanced settings that both checkers use"""
        # Advanced Settings
        advanced_frame = QFrame()
        advanced_frame.setStyleSheet(f"border: 1px solid {Colors.PRIMARY}; border-radius: 4px; padding: 5px;")
        advanced_layout = QGridLayout(advanced_frame)
        advanced_layout.setVerticalSpacing(2)
        advanced_layout.setHorizontalSpacing(10)

        self.ssl_check = QCheckBox("Verify SSL")
        self.delay_check = QCheckBox("Random Delays (1-3s)")
        self.header_rotation_check = QCheckBox("Rotate Headers")
        self.proxy_rotation_check = QCheckBox("Rotate Proxies")
        self.cloudscraper_check = QCheckBox("Cloudflare Bypass (v3 + Turnstile)")
        self.v3_challenges_check = QCheckBox("v3 JavaScript VM")
        self.turnstile_check = QCheckBox("Turnstile CAPTCHA")

        # Channel testing options (for Stalker)
        self.test_channels_check = QCheckBox("Test Live Channels")
        self.test_channels_check.setChecked(True)

        # Add tooltips
        self.add_tooltips_to_checkboxes()

        # Style checkboxes
        checkboxes = [self.ssl_check, self.delay_check, self.header_rotation_check,
                      self.proxy_rotation_check, self.cloudscraper_check,
                      self.v3_challenges_check, self.turnstile_check, self.test_channels_check]

        for i, checkbox in enumerate(checkboxes):
            checkbox.setStyleSheet(f"""
                QCheckBox {{
                    color: {Colors.TEXT};
                    font-size: 11px;
                }}
                QCheckBox::indicator {{
                    width: 14px;
                    height: 14px;
                }}
            """)
            checkbox.setChecked(True)
            advanced_layout.addWidget(checkbox, i // 2, i % 2)

        layout.addWidget(advanced_frame)

    def add_stalker_stats_and_log(self, layout):
        """Add Stalker-specific statistics and log display"""
        # Statistics
        stats_frame = QFrame()
        stats_frame.setStyleSheet(f"border: 1px solid {Colors.PRIMARY}; border-radius: 4px; padding: 5px;")
        stats_layout = QGridLayout(stats_frame)

        # Create Stalker stats labels
        self.stalker_hits_label = QLabel("Hits: 0")
        self.stalker_bad_label = QLabel("Bad: 0")
        self.stalker_errors_label = QLabel("Errors: 0")
        self.stalker_checked_label = QLabel("Checked: 0")
        self.stalker_cpm_label = QLabel("CPM: 0")

        # Style stats labels
        for label in [self.stalker_hits_label, self.stalker_bad_label, self.stalker_errors_label,
                     self.stalker_checked_label, self.stalker_cpm_label]:
            label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold; font-size: 12px;")

        stats_layout.addWidget(self.stalker_hits_label, 0, 0)
        stats_layout.addWidget(self.stalker_bad_label, 0, 1)
        stats_layout.addWidget(self.stalker_errors_label, 0, 2)
        stats_layout.addWidget(self.stalker_checked_label, 1, 0)
        stats_layout.addWidget(self.stalker_cpm_label, 1, 1)

        layout.addWidget(stats_frame)

        # Stalker Log Display
        self.stalker_log_display = QTextEdit()
        self.stalker_log_display.setMaximumHeight(200)
        self.stalker_log_display.setStyleSheet(f"""
            QTextEdit {{
                background-color: {Colors.SURFACE};
                border: 1px solid #CCCCCC;
                border-radius: 4px;
                color: {Colors.TEXT};
                font-family: 'Courier New', monospace;
                font-size: 10px;
            }}
        """)
        layout.addWidget(self.stalker_log_display)

    def add_xtream_stats_and_log(self, layout):
        """Add Xtream-specific statistics and log display"""
        # Statistics
        stats_frame = QFrame()
        stats_frame.setStyleSheet(f"border: 1px solid {Colors.PRIMARY}; border-radius: 4px; padding: 5px;")
        stats_layout = QGridLayout(stats_frame)

        # Create Xtream stats labels
        self.xtream_hits_label = QLabel("Hits: 0")
        self.xtream_bad_label = QLabel("Bad: 0")
        self.xtream_errors_label = QLabel("Errors: 0")
        self.xtream_checked_label = QLabel("Checked: 0")
        self.xtream_cpm_label = QLabel("CPM: 0")

        # Style stats labels
        for label in [self.xtream_hits_label, self.xtream_bad_label, self.xtream_errors_label,
                     self.xtream_checked_label, self.xtream_cpm_label]:
            label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold; font-size: 12px;")

        stats_layout.addWidget(self.xtream_hits_label, 0, 0)
        stats_layout.addWidget(self.xtream_bad_label, 0, 1)
        stats_layout.addWidget(self.xtream_errors_label, 0, 2)
        stats_layout.addWidget(self.xtream_checked_label, 1, 0)
        stats_layout.addWidget(self.xtream_cpm_label, 1, 1)

        layout.addWidget(stats_frame)

        # Xtream Log Display
        self.xtream_log_display = QTextEdit()
        self.xtream_log_display.setMaximumHeight(200)
        self.xtream_log_display.setStyleSheet(f"""
            QTextEdit {{
                background-color: {Colors.SURFACE};
                border: 1px solid #CCCCCC;
                border-radius: 4px;
                color: {Colors.TEXT};
                font-family: 'Courier New', monospace;
                font-size: 10px;
            }}
        """)
        layout.addWidget(self.xtream_log_display)

    def add_tooltips_to_checkboxes(self):
        """Add tooltips to all checkboxes"""
        self.ssl_check.setToolTip("Verify SSL certificates when making requests")
        self.delay_check.setToolTip("Add random delays between requests to avoid rate limiting")
        self.header_rotation_check.setToolTip("Rotate User-Agent headers to avoid detection")
        self.proxy_rotation_check.setToolTip("Rotate through proxy list for each request")

        # Enhanced cloudscraper25 tooltips
        self.cloudscraper_check.setToolTip(
            "Enhanced cloudscraper25 with v3 JavaScript VM and Turnstile support.\n"
            "This helps access streams protected by the latest Cloudflare anti-bot measures.\n"
            "Enhanced features include:\n"
            "• v3 JavaScript VM challenge solving\n"
            "• Turnstile CAPTCHA support\n"
            "• Stealth mode with human-like behavior\n"
            "• Browser fingerprint randomization\n"
            "• Advanced challenge detection and solving\n"
            "• Proxy rotation with smart strategies\n"
            "• Automatic retry with different browsers\n\n"
            "Recommended: Enabled"
        )

        self.v3_challenges_check.setToolTip(
            "Enable Cloudflare v3 JavaScript VM challenge support.\n"
            "v3 challenges are the latest and most sophisticated protection:\n"
            "• Run in a JavaScript Virtual Machine\n"
            "• Use advanced detection algorithms\n"
            "• Generate dynamic code that's harder to reverse-engineer\n"
            "• Provide the most current anti-bot technology\n\n"
            "This feature requires more processing time but handles the newest protection.\n"
            "Recommended: Enabled for maximum compatibility"
        )

        self.turnstile_check.setToolTip(
            "Enable Cloudflare Turnstile CAPTCHA support.\n"
            "Turnstile is Cloudflare's new CAPTCHA replacement:\n"
            "• More user-friendly than traditional CAPTCHAs\n"
            "• Invisible verification in most cases\n"
            "• Advanced bot detection technology\n"
            "• Automatic solving with supported CAPTCHA services\n\n"
            "Note: Requires CAPTCHA solving service for automatic bypass.\n"
            "Recommended: Enabled"
        )

        self.test_channels_check.setToolTip(
            "Test live channels to determine connection quality.\n"
            "When enabled, the checker will:\n"
            "• Test up to 5 random channels from each valid MAC\n"
            "• Determine connection quality (Excellent/Good/Fair/Poor/Dead)\n"
            "• Show working channel names in results\n"
            "• Provide detailed channel testing information\n\n"
            "Note: Channel testing adds extra time but provides valuable quality info.\n"
            "Recommended: Enabled for quality assessment"
        )

    def setup_xtream_tab(self):
        """Setup the Xtream Checker tab"""
        xtream_widget = QWidget()
        layout = QVBoxLayout(xtream_widget)

        # Server Input
        server_layout = QHBoxLayout()
        server_label = QLabel("Xtream Server:")
        server_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold;")
        self.xtream_server_input = QLineEdit()
        self.xtream_server_input.setPlaceholderText("http://example.com:8080")
        self.xtream_server_input.setStyleSheet(f"background-color: {Colors.SURFACE}; border: 1px solid #CCCCCC; border-radius: 4px; padding: 5px;")
        server_layout.addWidget(server_label)
        server_layout.addWidget(self.xtream_server_input)
        layout.addLayout(server_layout)

        # Combo Source
        combo_source_layout = QHBoxLayout()
        combo_source_label = QLabel("Combo Source:")
        combo_source_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold;")
        self.xtream_combo_source_combo = QComboBox()
        self.xtream_combo_source_combo.addItems(["Load from File", "Generate Combos"])
        self.xtream_combo_source_combo.currentIndexChanged.connect(self.toggle_xtream_combo_source)
        combo_source_layout.addWidget(combo_source_label)
        combo_source_layout.addWidget(self.xtream_combo_source_combo)
        layout.addLayout(combo_source_layout)

        # Combo Input Stack
        self.xtream_combo_input_stack = QStackedWidget()

        # File Panel
        file_panel = QWidget()
        file_layout = QHBoxLayout(file_panel)
        self.xtream_combo_file_btn = QPushButton("Load Combo File")
        set_button_style(self.xtream_combo_file_btn, Colors.PRIMARY)
        self.xtream_combo_file_btn.clicked.connect(self.load_xtream_combo_file)
        self.xtream_combo_file_label = QLabel("No file selected")
        self.xtream_combo_file_label.setStyleSheet(f"color: {Colors.TEXT};")
        file_layout.addWidget(self.xtream_combo_file_btn)
        file_layout.addWidget(self.xtream_combo_file_label)
        self.xtream_combo_input_stack.addWidget(file_panel)

        # Generate Panel
        generate_panel = QWidget()
        generate_layout = QHBoxLayout(generate_panel)
        self.xtream_generate_btn = QPushButton("Generate Combos")
        set_button_style(self.xtream_generate_btn, Colors.SUCCESS)
        self.xtream_generate_btn.clicked.connect(self.open_xtream_combo_generator)
        generate_layout.addWidget(self.xtream_generate_btn)
        generate_layout.addStretch()
        self.xtream_combo_input_stack.addWidget(generate_panel)

        layout.addWidget(self.xtream_combo_input_stack)

        # Threads and Rate Limiting
        thread_rate_layout = QHBoxLayout()

        # Threads
        thread_label = QLabel("Threads:")
        thread_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold;")
        self.xtream_thread_input = QSpinBox()
        self.xtream_thread_input.setRange(1, 50)
        self.xtream_thread_input.setValue(5)
        self.xtream_thread_input.setFixedWidth(100)

        # Rate Limiting
        rate_label = QLabel("Rate (req/sec):")
        rate_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold;")
        self.xtream_rate_input = QSpinBox()
        self.xtream_rate_input.setRange(1, 20)
        self.xtream_rate_input.setValue(2)
        self.xtream_rate_input.setFixedWidth(100)

        thread_rate_layout.addWidget(thread_label)
        thread_rate_layout.addWidget(self.xtream_thread_input)
        thread_rate_layout.addWidget(rate_label)
        thread_rate_layout.addWidget(self.xtream_rate_input)
        thread_rate_layout.addStretch()
        layout.addLayout(thread_rate_layout)

        # Proxy Section
        proxy_layout = QHBoxLayout()
        self.xtream_use_proxies = QCheckBox("Use Proxies")
        self.xtream_use_proxies.setStyleSheet(f"color: {Colors.TEXT};")
        self.xtream_proxy_file_btn = QPushButton("Load Proxies")
        set_button_style(self.xtream_proxy_file_btn, Colors.PRIMARY)
        self.xtream_proxy_file_btn.clicked.connect(self.load_proxies)
        proxy_layout.addWidget(self.xtream_use_proxies)
        proxy_layout.addWidget(self.xtream_proxy_file_btn)
        layout.addLayout(proxy_layout)

        # Auto-construct URLs option
        auto_url_layout = QHBoxLayout()
        self.xtream_auto_urls = QCheckBox("Auto-construct URLs from combos")
        self.xtream_auto_urls.setStyleSheet(f"color: {Colors.TEXT}; font-size: 12px;")
        self.xtream_auto_urls.setToolTip("Enable this to automatically construct server URLs from combo entries like 'server:port:username:password'")
        auto_url_layout.addWidget(self.xtream_auto_urls)
        auto_url_layout.addStretch()
        layout.addLayout(auto_url_layout)

        # Add the shared advanced settings (same as Stalker)
        self.add_shared_advanced_settings(layout)

        # Control Buttons
        button_layout = QHBoxLayout()
        self.xtream_start_btn = QPushButton("Start Xtream Check")
        self.xtream_stop_btn = QPushButton("Stop")
        set_button_style(self.xtream_start_btn, Colors.SUCCESS)
        set_button_style(self.xtream_stop_btn, Colors.ERROR)
        self.xtream_start_btn.clicked.connect(self.start_xtream_checking)
        self.xtream_stop_btn.clicked.connect(self.stop_xtream_checking)
        self.xtream_stop_btn.setEnabled(False)
        button_layout.addWidget(self.xtream_start_btn)
        button_layout.addWidget(self.xtream_stop_btn)
        layout.addLayout(button_layout)

        # Add Xtream-specific stats and log
        self.add_xtream_stats_and_log(layout)

        self.tab_widget.addTab(xtream_widget, "🌐 Xtream Checker")

    def setup_player_tab(self):
        """Setup the IPTV Player tab"""
        # Create the player widget
        self.player_widget = PlayerWidget()

        # Add the player tab
        self.tab_widget.addTab(self.player_widget, "🎬 IPTV Player")

    def setup_other_tools_tab(self):
        """Setup the Other Tools tab"""
        # Create the other tools widget
        self.other_tools_widget = OtherToolsWidget()

        # Add the other tools tab
        self.tab_widget.addTab(self.other_tools_widget, "🛠️ Other Tools")

    # Toggle Methods
    def toggle_stalker_mac_source(self):
        """Toggle between MAC generation and file loading for Stalker"""
        self.stalker_mac_input_stack.setCurrentIndex(self.stalker_mac_source_combo.currentIndex())

    def toggle_xtream_combo_source(self):
        """Toggle between combo file loading and generation for Xtream"""
        self.xtream_combo_input_stack.setCurrentIndex(self.xtream_combo_source_combo.currentIndex())

    # File Loading Methods
    def load_stalker_mac_file(self):
        """Load MAC file for Stalker checker"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Select MAC File", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            self.mac_file_path = file_path
            self.stalker_mac_file_label.setText(f"Loaded: {os.path.basename(file_path)}")

    def load_xtream_combo_file(self):
        """Load combo file for Xtream checker"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Select Combo File", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            self.xtream_combo_file_path = file_path
            self.xtream_combo_file_label.setText(f"Loaded: {os.path.basename(file_path)}")

    def load_proxies(self):
        """Load proxy file (shared by both checkers)"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Select Proxy File", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            success = self.proxy_handler.load_proxies(file_path)
            if success:
                QMessageBox.information(self, "Success", f"Loaded {len(self.proxy_handler.proxies)} proxies")
            else:
                QMessageBox.warning(self, "Error", "Failed to load proxies")

    def open_xtream_combo_generator(self):
        """Open the Xtream combo generator dialog"""
        dialog = XtreamComboGeneratorDialog(self)
        dialog.exec()

    # Stalker Checker Methods
    def start_stalker_checking(self):
        """Start the Stalker Portal MAC checking process"""
        portal_url = self.stalker_url_input.text().strip()
        if not portal_url:
            QMessageBox.warning(self, "Error", "Please enter a portal URL")
            return

        # Validate MAC input or file
        if self.stalker_mac_source_combo.currentIndex() == 0:  # Generate MACs
            start_mac = self.stalker_mac_input.text()
            if not re.match(r'^[0-9A-Fa-f]{2}:[0-9A-Fa-f]{2}:[0-9A-Fa-f]{2}$', start_mac):
                QMessageBox.warning(self, "Error", "Invalid MAC format. Use XX:XX:XX format")
                return
        else:  # Load from file
            if not hasattr(self, 'mac_file_path') or not self.mac_file_path:
                QMessageBox.warning(self, "Error", "Please select a MAC file")
                return

        # Reset Stalker counters
        self.stalker_check_count = 0

        # Get configuration
        checker_config = self.get_checker_config()

        # Determine MAC source and parameter
        if self.stalker_mac_source_combo.currentIndex() == 0:  # Generate MACs
            mac_source = 0
            mac_param = self.stalker_mac_input.text()
        else:  # Load from file
            mac_source = 1
            mac_param = self.mac_file_path

        # Create and start worker
        self.stalker_worker = CheckerWorker(
            base_url=portal_url,
            mac_source=mac_source,
            mac_param=mac_param,
            use_proxies=self.stalker_use_proxies.isChecked(),
            num_threads=self.stalker_thread_input.value(),
            checker_config=checker_config
        )

        self.stalker_worker.update_signal.connect(self.update_stalker_log)
        self.stalker_worker.progress_signal.connect(self.update_stalker_progress)
        self.stalker_worker.finished.connect(self.stalker_checking_finished)

        self.stalker_worker.start()

        # Update UI
        self.stalker_start_btn.setEnabled(False)
        self.stalker_stop_btn.setEnabled(True)
        self.cpm_timer.start(1000)  # Update CPM every second

        self.stalker_log_display.append(f"[{datetime.now().strftime('%H:%M:%S')}] Started Stalker checking...")

    def stop_stalker_checking(self):
        """Stop the Stalker checking process (non-blocking)"""
        if self.stalker_worker:
            # Update UI immediately to prevent freezing
            self.stalker_start_btn.setEnabled(True)
            self.stalker_stop_btn.setEnabled(False)
            self.stalker_log_display.append(f"[{datetime.now().strftime('%H:%M:%S')}] Stopping Stalker checking...")

            # Stop the worker
            self.stalker_worker.stop()

            # Connect to finished signal for cleanup
            self.stalker_worker.finished.connect(self.on_stalker_worker_stopped)

            # Don't wait() here as it blocks the UI
            # The worker will emit finished signal when done
        else:
            self.stalker_checking_finished()

    def on_stalker_worker_stopped(self):
        """Handle stalker worker cleanup after it stops"""
        if self.stalker_worker:
            self.stalker_worker.deleteLater()
            self.stalker_worker = None
        self.stalker_checking_finished()

    def stalker_checking_finished(self):
        """Handle Stalker checking completion"""
        self.stalker_start_btn.setEnabled(True)
        self.stalker_stop_btn.setEnabled(False)
        self.cpm_timer.stop()
        self.stalker_log_display.append(f"[{datetime.now().strftime('%H:%M:%S')}] Stalker checking stopped.")

    # Xtream Checker Methods
    def start_xtream_checking(self):
        """Start the Xtream checking process"""
        server_url = self.xtream_server_input.text().strip()
        if not server_url:
            QMessageBox.warning(self, "Error", "Please enter a server URL")
            return

        # Validate combo input
        if self.xtream_combo_source_combo.currentIndex() == 0:  # Load from file
            if not hasattr(self, 'xtream_combo_file_path') or not self.xtream_combo_file_path:
                QMessageBox.warning(self, "Error", "Please select a combo file")
                return

            # Load combos from file
            try:
                with open(self.xtream_combo_file_path, 'r', encoding='utf-8') as f:
                    combos = [line.strip() for line in f if line.strip() and ':' in line.strip()]

                if not combos:
                    QMessageBox.warning(self, "Error", "No valid combos found in file")
                    return

            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load combo file: {str(e)}")
                return
        else:  # Generate combos
            QMessageBox.information(self, "Info", "Please generate combos first using the Generate Combos button")
            return

        # Reset counters
        self.xtream_hit_count = 0
        self.xtream_bad_count = 0
        self.xtream_error_count = 0
        self.xtream_check_count = 0

        # Get configuration
        checker_config = self.get_checker_config()

        # Create and configure worker
        self.xtream_worker = XtreamWorker(
            server_url=server_url,
            combos=combos,
            num_threads=self.xtream_thread_input.value(),
            rate_limit=self.xtream_rate_input.value(),
            proxy_handler=self.proxy_handler if self.xtream_use_proxies.isChecked() else None,
            checker_config=checker_config,
            auto_construct_urls=self.xtream_auto_urls.isChecked()
        )

        # Connect signals
        self.xtream_worker.update_signal.connect(self.update_xtream_result)
        self.xtream_worker.stats_signal.connect(self.update_xtream_stats_from_worker)
        self.xtream_worker.progress_signal.connect(self.update_xtream_progress)
        self.xtream_worker.finished_signal.connect(self.xtream_checking_finished)

        # Start worker
        self.xtream_worker.start()

        # Update UI
        self.xtream_start_btn.setEnabled(False)
        self.xtream_stop_btn.setEnabled(True)
        self.cpm_timer.start(1000)

        self.xtream_log_display.append(f"[{datetime.now().strftime('%H:%M:%S')}] Starting Xtream checking with {len(combos)} combos...")

    def stop_xtream_checking(self):
        """Stop the Xtream checking process (non-blocking)"""
        if self.xtream_worker:
            # Update UI immediately to prevent freezing
            self.xtream_start_btn.setEnabled(True)
            self.xtream_stop_btn.setEnabled(False)
            self.xtream_log_display.append(f"[{datetime.now().strftime('%H:%M:%S')}] Stopping Xtream checking...")

            # Stop the worker
            self.xtream_worker.stop()

            # Connect to finished signal for cleanup
            self.xtream_worker.finished_signal.connect(self.on_xtream_worker_stopped)

            # Don't wait() here as it blocks the UI
            # The worker will emit finished signal when done
        else:
            self.xtream_checking_finished()

    def on_xtream_worker_stopped(self):
        """Handle xtream worker cleanup after it stops"""
        if self.xtream_worker:
            self.xtream_worker.deleteLater()
            self.xtream_worker = None
        self.xtream_checking_finished()

    def xtream_checking_finished(self):
        """Handle Xtream checking completion"""
        self.xtream_start_btn.setEnabled(True)
        self.xtream_stop_btn.setEnabled(False)
        self.cpm_timer.stop()
        self.xtream_log_display.append(f"[{datetime.now().strftime('%H:%M:%S')}] Xtream checking finished.")

    def update_xtream_result(self, message, result_type, status):
        """Update Xtream checker results"""
        # Note: status parameter kept for compatibility with signal
        timestamp = datetime.now().strftime('%H:%M:%S')

        if result_type == "hit":
            self.xtream_hit_count += 1
            self.xtream_log_display.append(f"[{timestamp}] ✅ HIT:\n{message}")

            # Save hit to file
            server_name = format_server_name(self.xtream_server_input.text())
            filename = get_formatted_filename(server_name, "xtream_hits")
            os.makedirs("hits", exist_ok=True)

            with open(f"hits/{filename}", "a", encoding="utf-8") as f:
                f.write(f"{message}\n{'='*50}\n")

        elif result_type == "bad":
            self.xtream_bad_count += 1
            self.xtream_log_display.append(f"[{timestamp}] ❌ BAD: {message}")
        elif result_type == "error":
            self.xtream_error_count += 1
            self.xtream_log_display.append(f"[{timestamp}] ⚠️ ERROR: {message}")

        self.xtream_check_count += 1
        self.update_xtream_stats()

    def update_xtream_progress(self, current, total):
        """Update Xtream progress"""
        # Update progress display
        progress_text = f"Progress: {current}/{total} ({int(current/total*100) if total > 0 else 0}%)"
        # You could add a progress bar here if desired

    def update_xtream_stats_from_worker(self, hits, bad, errors, checked):
        """Update Xtream statistics from worker signals"""
        self.xtream_hit_count = hits
        self.xtream_bad_count = bad
        self.xtream_error_count = errors
        self.xtream_check_count = checked
        self.update_xtream_stats()

    def update_xtream_stats(self):
        """Update Xtream statistics display"""
        self.xtream_hits_label.setText(f"Hits: {self.xtream_hit_count}")
        self.xtream_bad_label.setText(f"Bad: {self.xtream_bad_count}")
        self.xtream_errors_label.setText(f"Errors: {self.xtream_error_count}")
        self.xtream_checked_label.setText(f"Checked: {self.xtream_check_count}")

    # Shared Methods
    def get_checker_config(self):
        """Get checker configuration from UI"""
        return {
            'verify_ssl': self.ssl_check.isChecked(),
            'request_delay': (1, 3) if self.delay_check.isChecked() else (0, 0),
            'rotate_headers': self.header_rotation_check.isChecked(),
            'rotate_proxies': self.proxy_rotation_check.isChecked(),
            'use_cloudscraper': self.cloudscraper_check.isChecked(),
            'enable_v3_challenges': self.v3_challenges_check.isChecked(),
            'enable_turnstile': self.turnstile_check.isChecked(),
            'test_channels': self.test_channels_check.isChecked(),
            'javascript_interpreter': 'js2py'
        }

    def update_stalker_log(self, message, message_type, status):
        """Update log display for Stalker checker"""
        timestamp = datetime.now().strftime('%H:%M:%S')

        if status == 1:  # Hit
            self.stalker_log_display.append(f"[{timestamp}] ✅ HIT: {message}")
        elif message_type == "error":
            self.stalker_log_display.append(f"[{timestamp}] ⚠️ ERROR: {message}")
        else:
            self.stalker_log_display.append(f"[{timestamp}] ℹ️ {message}")

    def update_stalker_progress(self, hits, tries):
        """Update Stalker progress and statistics"""
        # Update the check count for CPM calculation
        self.stalker_check_count = tries

        # Calculate bad and errors (simplified for now)
        bad = tries - hits
        errors = 0  # CheckerWorker doesn't separate bad from errors

        # Update display
        self.stalker_hits_label.setText(f"Hits: {hits}")
        self.stalker_bad_label.setText(f"Bad: {bad}")
        self.stalker_errors_label.setText(f"Errors: {errors}")
        self.stalker_checked_label.setText(f"Checked: {tries}")

    # Legacy methods for compatibility
    def update_log(self, message):
        """Legacy method - redirect to stalker log"""
        self.update_stalker_log(message, "info", 0)

    def update_stats(self, hits, bad, errors, checked):
        """Legacy method - update statistics display"""
        self.hits_label.setText(f"Hits: {hits}")
        self.bad_label.setText(f"Bad: {bad}")
        self.errors_label.setText(f"Errors: {errors}")
        self.checked_label.setText(f"Checked: {checked}")

    def update_cpm(self):
        """Update CPM (Checks Per Minute) display with improved calculation"""
        current_time = time.time()
        time_diff = current_time - self.last_time

        # Determine which checker is active and get current count
        if hasattr(self, 'stalker_worker') and self.stalker_worker and self.stalker_worker.isRunning():
            current_tries = self.stalker_check_count
            cpm_label = self.stalker_cpm_label
        elif hasattr(self, 'xtream_worker') and self.xtream_worker and self.xtream_worker.isRunning():
            current_tries = self.xtream_check_count
            cpm_label = self.xtream_cpm_label
        else:
            # No active checker - update both labels
            self.stalker_cpm_label.setText("CPM: 0 (Idle)")
            self.xtream_cpm_label.setText("CPM: 0 (Idle)")
            return

        tries_diff = current_tries - self.last_tries

        # Calculate CPM with minimum time threshold to avoid division issues
        if time_diff >= 1.0:  # At least 1 second passed
            cpm = int((tries_diff / time_diff) * 60)

            # Ensure CPM is not negative
            if cpm < 0:
                cpm = 0

            cpm_label.setText(f"CPM: {cpm}")

            # Update tracking variables
            self.last_time = current_time
            self.last_tries = current_tries
        else:
            # Too little time passed, keep previous CPM but update display
            if hasattr(self, '_last_cpm'):
                cpm_label.setText(f"CPM: {self._last_cpm}")
            else:
                cpm_label.setText(f"CPM: ...")

        # Store last CPM for display consistency
        if 'cpm' in locals():
            self._last_cpm = cpm

    def show_about_dialog(self):
        """Show about dialog"""
        dialog = AboutDialog(self)
        dialog.exec()

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')

    # Set application-wide dark theme
    app.setStyleSheet(f"""
        QApplication {{
            background-color: {Colors.BACKGROUND};
            color: {Colors.TEXT};
        }}
    """)

    window = MainWindow()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
