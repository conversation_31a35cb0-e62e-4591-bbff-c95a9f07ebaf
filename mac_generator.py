def generate_mac_combinations(prefix: str = "00:1A:79:", start_from: str = None):
    """
    Generate MAC address combinations starting from a given prefix and optional starting point.

    Args:
        prefix: The MAC prefix (default: "00:1A:79:")
        start_from: The starting point for generation (format: XX:XX:XX or full MAC)

    Yields:
        Generated MAC addresses
    """
    # Default starting values
    start = middle = end = 0

    # Parse the starting point if provided
    if start_from:
        try:
            # Handle full MAC input (00:1A:79:XX:XX:XX)
            if start_from.count(":") == 5:
                parts = start_from.split(":")
                start = int(parts[3], 16)
                middle = int(parts[4], 16)
                end = int(parts[5], 16)
            # Handle partial MAC input (XX:XX:XX)
            elif start_from.count(":") == 2:
                parts = start_from.split(":")
                start = int(parts[0], 16)
                middle = int(parts[1], 16)
                end = int(parts[2], 16)
            # Handle other formats
            else:
                print(f"Warning: Unexpected MAC format '{start_from}', using default starting point")
        except (ValueError, IndexError) as e:
            print(f"Invalid start_from format: {e}")
            # Continue with default values instead of returning

    print(f"Generating MACs starting from: {prefix}{start:02X}:{middle:02X}:{end:02X}")

    # Generate all combinations
    try:
        for i in range(start, 256):
            for j in range(middle if i == start else 0, 256):
                for k in range(end if i == start and j == middle else 0, 256):
                    mac = f"{prefix}{i:02X}:{j:02X}:{k:02X}"
                    yield mac
    except Exception as e:
        print(f"Error generating MAC combinations: {e}")
        # Yield a few valid MACs as fallback
        for i in range(10):
            yield f"{prefix}00:00:{i:02X}"

def read_mac_file(file_path: str):
    try:
        with open(file_path, 'r') as f:
            for line_num, line in enumerate(f, 1):
                raw_mac = line.strip()
                if not raw_mac:
                    continue

                # Normalize MAC to uppercase
                mac = raw_mac.upper()

                # Validate MAC format
                if mac.count(":") != 5:
                    print(f"Invalid MAC format at line {line_num}: {raw_mac}")
                    continue

                if not mac.startswith("00:1A:79:"):
                    print(f"MAC doesn't match stalker format at line {line_num}: {raw_mac}")
                    continue

                try:
                    # Validate hexadecimal parts
                    [int(part, 16) for part in mac.split(":")]
                    yield mac
                except ValueError:
                    print(f"Invalid hexadecimal values in MAC at line {line_num}: {raw_mac}")
    except Exception as e:
        print(f"MAC file error: {e}")
        yield from []