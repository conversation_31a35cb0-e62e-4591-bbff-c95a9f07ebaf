"""
Other Tools Module
Contains various utility tools: MAC generator, Portal converter, Proxy grabber
"""

import logging
import requests
import random
import os
from datetime import datetime
from urllib.parse import urlparse
from PyQt6.QtCore import QThread, pyqtSignal
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QTextEdit, QComboBox, QSpinBox, QMessageBox,
    QProgressBar, QGroupBox, QScrollArea, QFileDialog
)

from styles import Colors

# Setup logging
logger = logging.getLogger(__name__)

class ProxyGrabberThread(QThread):
    """Thread for grabbing proxies from various sources"""
    proxies_found = pyqtSignal(list)
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    finished = pyqtSignal()

    def __init__(self, proxy_type):
        super().__init__()
        self.proxy_type = proxy_type
        self.proxies = []

    def run(self):
        """Run proxy grabbing"""
        try:
            self.status_updated.emit(f"🔍 Searching for {self.proxy_type} proxies...")
            self.progress_updated.emit(10)

            # Proxy sources (free proxy APIs and websites)
            sources = self.get_proxy_sources()
            total_sources = len(sources)

            for i, source in enumerate(sources):
                try:
                    self.status_updated.emit(f"📡 Checking source {i+1}/{total_sources}...")
                    proxies = self.fetch_proxies_from_source(source)
                    self.proxies.extend(proxies)
                    progress = int(((i + 1) / total_sources) * 90)
                    self.progress_updated.emit(progress)
                except Exception as e:
                    logger.warning(f"Failed to fetch from source {source}: {e}")
                    continue

            # Remove duplicates
            unique_proxies = list(set(self.proxies))
            self.proxies = unique_proxies

            self.progress_updated.emit(100)
            self.status_updated.emit(f"✅ Found {len(self.proxies)} {self.proxy_type} proxies")
            self.proxies_found.emit(self.proxies)

        except Exception as e:
            logger.error(f"Error in proxy grabber: {e}")
            self.error_occurred.emit(str(e))
        finally:
            self.finished.emit()

    def get_proxy_sources(self):
        """Get proxy sources based on type"""
        if self.proxy_type in ["HTTP", "HTTPS"]:
            return [
                "https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=10000&country=all&ssl=all&anonymity=all",
                "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt",
                "https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt",
                "https://raw.githubusercontent.com/sunny9577/proxy-scraper/master/proxies.txt"
            ]
        else:  # SOCKS4/SOCKS5
            return [
                "https://api.proxyscrape.com/v2/?request=get&protocol=socks4&timeout=10000&country=all",
                "https://api.proxyscrape.com/v2/?request=get&protocol=socks5&timeout=10000&country=all",
                "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/socks4.txt",
                "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/socks5.txt"
            ]

    def fetch_proxies_from_source(self, url):
        """Fetch proxies from a single source"""
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()

            # Parse proxy list
            proxies = []
            lines = response.text.strip().split('\n')

            for line in lines:
                line = line.strip()
                if ':' in line and self.is_valid_proxy(line):
                    proxies.append(line)

            return proxies
        except Exception as e:
            logger.warning(f"Failed to fetch from {url}: {e}")
            return []

    def is_valid_proxy(self, proxy):
        """Validate proxy format"""
        try:
            if ':' not in proxy:
                return False

            parts = proxy.split(':')
            if len(parts) != 2:
                return False

            ip, port = parts

            # Validate IP
            ip_parts = ip.split('.')
            if len(ip_parts) != 4:
                return False

            for part in ip_parts:
                if not part.isdigit() or not 0 <= int(part) <= 255:
                    return False

            # Validate port
            if not port.isdigit() or not 1 <= int(port) <= 65535:
                return False

            return True
        except:
            return False

class PortalConverterThread(QThread):
    """Thread for converting portal URL + MAC to username/password"""
    conversion_completed = pyqtSignal(dict)
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    finished = pyqtSignal()

    def __init__(self, portal_url, mac_address):
        super().__init__()
        self.portal_url = portal_url
        self.mac_address = mac_address

    def run(self):
        """Run portal conversion"""
        try:
            self.status_updated.emit("🔍 Analyzing portal...")
            self.progress_updated.emit(20)

            # Try to connect to portal and analyze VOD URLs
            result = self.analyze_portal()

            self.progress_updated.emit(100)
            self.conversion_completed.emit(result)

        except Exception as e:
            logger.error(f"Error in portal converter: {e}")
            self.error_occurred.emit(str(e))
        finally:
            self.finished.emit()

    def analyze_portal(self):
        """Analyze portal to extract credentials from VOD URLs"""
        try:
            # Simulate portal connection (this would need actual Stalker API implementation)
            self.status_updated.emit("🔗 Connecting to portal...")
            self.progress_updated.emit(40)

            # For demonstration, we'll extract from common URL patterns
            # In real implementation, this would connect to the portal and analyze VOD URLs

            self.status_updated.emit("📺 Analyzing VOD URLs...")
            self.progress_updated.emit(60)

            # Extract potential credentials from URL patterns
            parsed_url = urlparse(self.portal_url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"

            # Common patterns for extracting credentials
            # This is a simplified example - real implementation would be more complex
            result = {
                'success': True,
                'original_url': self.portal_url,
                'original_mac': self.mac_address,
                'base_url': base_url,
                'username': None,
                'password': None,
                'converted_url': None,
                'message': 'Portal analysis completed'
            }

            self.status_updated.emit("✅ Analysis completed")
            return result

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': f'Failed to analyze portal: {e}'
            }

class OtherToolsWidget(QWidget):
    """Widget containing various utility tools"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """Initialize the user interface"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Create scroll area for tools
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background-color: {Colors.BACKGROUND};
            }}
        """)

        # Main content widget
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(15)

        # Remove title to save space - tab already shows "Other Tools"

        # MAC Address Generator
        self.setup_mac_generator(content_layout)

        # Portal Converter
        self.setup_portal_converter(content_layout)

        # Proxy Grabber
        self.setup_proxy_grabber(content_layout)

        # Set content widget to scroll area
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)

    def setup_mac_generator(self, layout):
        """Setup MAC address generator section"""
        # MAC Generator Group
        mac_group = QGroupBox("🔧 MAC Address Generator")
        mac_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 14px;
                color: {Colors.TEXT};
                border: 2px solid {Colors.PRIMARY};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        mac_layout = QVBoxLayout(mac_group)
        mac_layout.setSpacing(10)

        # Description
        desc_label = QLabel("Generate unlimited random MAC addresses with format: 00:1A:79:XX:XX:XX (Auto-saved to macs/ folder)")
        desc_label.setStyleSheet(f"color: {Colors.TEXT}; font-size: 12px; padding: 5px;")
        mac_layout.addWidget(desc_label)

        # Input row
        input_row = QHBoxLayout()

        # Number of MACs
        count_label = QLabel("Number of MACs:")
        count_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold;")
        self.mac_count_spin = QSpinBox()
        self.mac_count_spin.setRange(1, 999999999)  # Practically unlimited
        self.mac_count_spin.setValue(10)
        self.mac_count_spin.setStyleSheet(f"""
            QSpinBox {{
                background-color: white;
                border: 2px solid {Colors.PRIMARY};
                border-radius: 4px;
                padding: 5px;
                font-size: 12px;
                min-width: 80px;
            }}
        """)

        # Generate button
        self.generate_mac_btn = QPushButton("🎲 Generate MACs")
        self.generate_mac_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.SUCCESS};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {Colors.SUCCESS}DD;
            }}
        """)
        self.generate_mac_btn.clicked.connect(self.generate_macs)

        # Save button
        self.save_mac_btn = QPushButton("💾 Save MACs")
        self.save_mac_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.PRIMARY};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {Colors.PRIMARY}DD;
            }}
        """)
        self.save_mac_btn.clicked.connect(self.save_macs)
        self.save_mac_btn.setEnabled(False)  # Initially disabled

        input_row.addWidget(count_label)
        input_row.addWidget(self.mac_count_spin)
        input_row.addStretch()
        input_row.addWidget(self.generate_mac_btn)
        input_row.addWidget(self.save_mac_btn)
        mac_layout.addLayout(input_row)

        # Output area
        self.mac_output = QTextEdit()
        self.mac_output.setMaximumHeight(150)
        self.mac_output.setStyleSheet(f"""
            QTextEdit {{
                background-color: {Colors.SURFACE};
                border: 1px solid {Colors.PRIMARY};
                border-radius: 4px;
                padding: 8px;
                font-family: monospace;
                font-size: 11px;
                color: {Colors.TEXT};
            }}
        """)
        self.mac_output.setPlaceholderText("Generated MAC addresses will appear here...")
        mac_layout.addWidget(self.mac_output)

        layout.addWidget(mac_group)

    def generate_macs(self):
        """Generate random MAC addresses"""
        try:
            count = self.mac_count_spin.value()
            prefix = "00:1A:79:"

            # Warning for very large numbers
            if count > 100000:
                reply = QMessageBox.question(
                    self,
                    "Large Number Warning",
                    f"You are about to generate {count:,} MAC addresses.\n"
                    f"This may take a while and use significant memory.\n\n"
                    f"Do you want to continue?",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.No
                )
                if reply == QMessageBox.StandardButton.No:
                    return

            macs = []
            for _ in range(count):
                # Generate random last 3 octets
                octet1 = random.randint(0, 255)
                octet2 = random.randint(0, 255)
                octet3 = random.randint(0, 255)
                mac = f"{prefix}{octet1:02X}:{octet2:02X}:{octet3:02X}"
                macs.append(mac)

            # Store generated MACs for saving
            self.generated_macs = macs

            # Display generated MACs
            self.mac_output.clear()
            self.mac_output.setPlainText('\n'.join(macs))

            # Enable save button
            self.save_mac_btn.setEnabled(True)

            # Auto-save MACs
            self.auto_save_macs(macs, count)

        except Exception as e:
            logger.error(f"Error generating MACs: {e}")
            QMessageBox.warning(self, "Error", f"Failed to generate MACs: {str(e)}")

    def auto_save_macs(self, macs, count):
        """Automatically save generated MACs to a file"""
        try:
            # Create macs directory if it doesn't exist
            if not os.path.exists("macs"):
                os.makedirs("macs")

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"macs/generated_macs_{count}_{timestamp}.txt"

            # Save MACs to file
            with open(filename, 'w') as f:
                f.write('\n'.join(macs))

            logger.info(f"Auto-saved {count} MACs to {filename}")

            # Update placeholder text to show save location
            self.mac_output.setPlaceholderText(f"Generated MAC addresses auto-saved to: {filename}")

        except Exception as e:
            logger.error(f"Error auto-saving MACs: {e}")

    def save_macs(self):
        """Manually save generated MACs to a custom location"""
        try:
            if not hasattr(self, 'generated_macs') or not self.generated_macs:
                QMessageBox.warning(self, "No MACs", "No MAC addresses to save. Generate some first!")
                return

            # Get save location from user
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"generated_macs_{len(self.generated_macs)}_{timestamp}.txt"

            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Save MAC Addresses",
                default_filename,
                "Text Files (*.txt);;All Files (*)"
            )

            if file_path:
                # Save MACs to selected file
                with open(file_path, 'w') as f:
                    f.write('\n'.join(self.generated_macs))

                QMessageBox.information(
                    self,
                    "Saved Successfully",
                    f"Saved {len(self.generated_macs)} MAC addresses to:\n{file_path}"
                )
                logger.info(f"Manually saved {len(self.generated_macs)} MACs to {file_path}")

        except Exception as e:
            logger.error(f"Error saving MACs: {e}")
            QMessageBox.warning(self, "Error", f"Failed to save MACs: {str(e)}")

    def setup_portal_converter(self, layout):
        """Setup portal converter section"""
        # Portal Converter Group
        portal_group = QGroupBox("🔄 Portal Converter")
        portal_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 14px;
                color: {Colors.TEXT};
                border: 2px solid {Colors.PRIMARY};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        portal_layout = QVBoxLayout(portal_group)
        portal_layout.setSpacing(10)

        # Description
        desc_label = QLabel("Convert Portal URL + MAC to Username/Password format")
        desc_label.setStyleSheet(f"color: {Colors.TEXT}; font-size: 12px; padding: 5px;")
        portal_layout.addWidget(desc_label)

        # Portal URL input
        url_layout = QHBoxLayout()
        url_label = QLabel("Portal URL:")
        url_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold;")
        self.portal_url_input = QLineEdit()
        self.portal_url_input.setPlaceholderText("http://example.com:8080/stalker_portal/c/")
        self.portal_url_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: white;
                border: 2px solid {Colors.PRIMARY};
                border-radius: 4px;
                padding: 8px;
                font-size: 12px;
            }}
        """)
        url_layout.addWidget(url_label)
        url_layout.addWidget(self.portal_url_input)
        portal_layout.addLayout(url_layout)

        # MAC address input
        mac_layout = QHBoxLayout()
        mac_label = QLabel("MAC Address:")
        mac_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold;")
        self.portal_mac_input = QLineEdit()
        self.portal_mac_input.setPlaceholderText("00:1A:79:XX:XX:XX")
        self.portal_mac_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: white;
                border: 2px solid {Colors.PRIMARY};
                border-radius: 4px;
                padding: 8px;
                font-size: 12px;
            }}
        """)
        mac_layout.addWidget(mac_label)
        mac_layout.addWidget(self.portal_mac_input)
        portal_layout.addLayout(mac_layout)

        # Convert button and progress
        button_layout = QHBoxLayout()
        self.convert_portal_btn = QPushButton("🔄 Convert Portal")
        self.convert_portal_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.PRIMARY};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {Colors.PRIMARY}DD;
            }}
        """)
        self.convert_portal_btn.clicked.connect(self.convert_portal)

        self.portal_progress = QProgressBar()
        self.portal_progress.setVisible(False)
        self.portal_progress.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {Colors.PRIMARY};
                border-radius: 5px;
                text-align: center;
            }}
            QProgressBar::chunk {{
                background-color: {Colors.SUCCESS};
                border-radius: 3px;
            }}
        """)

        button_layout.addWidget(self.convert_portal_btn)
        button_layout.addWidget(self.portal_progress)
        button_layout.addStretch()
        portal_layout.addLayout(button_layout)

        # Output area
        self.portal_output = QTextEdit()
        self.portal_output.setMaximumHeight(150)
        self.portal_output.setStyleSheet(f"""
            QTextEdit {{
                background-color: {Colors.SURFACE};
                border: 1px solid {Colors.PRIMARY};
                border-radius: 4px;
                padding: 8px;
                font-family: monospace;
                font-size: 11px;
                color: {Colors.TEXT};
            }}
        """)
        self.portal_output.setPlaceholderText("Conversion results will appear here...")
        portal_layout.addWidget(self.portal_output)

        layout.addWidget(portal_group)

    def convert_portal(self):
        """Convert portal URL and MAC to username/password"""
        try:
            portal_url = self.portal_url_input.text().strip()
            mac_address = self.portal_mac_input.text().strip()

            if not portal_url or not mac_address:
                QMessageBox.warning(self, "Input Error", "Please enter both Portal URL and MAC address")
                return

            # Show progress
            self.portal_progress.setVisible(True)
            self.portal_progress.setValue(0)
            self.convert_portal_btn.setEnabled(False)

            # Create and start converter thread
            self.portal_converter_thread = PortalConverterThread(portal_url, mac_address)
            self.portal_converter_thread.progress_updated.connect(self.portal_progress.setValue)
            self.portal_converter_thread.conversion_completed.connect(self.on_portal_conversion_completed)
            self.portal_converter_thread.error_occurred.connect(self.on_portal_conversion_error)
            self.portal_converter_thread.finished.connect(self.on_portal_conversion_finished)
            self.portal_converter_thread.start()

        except Exception as e:
            logger.error(f"Error starting portal conversion: {e}")
            QMessageBox.warning(self, "Error", f"Failed to start conversion: {str(e)}")
            self.on_portal_conversion_finished()

    def on_portal_conversion_completed(self, result):
        """Handle portal conversion completion"""
        try:
            output_text = f"Portal Conversion Results:\n"
            output_text += f"{'='*50}\n"
            output_text += f"Original URL: {result.get('original_url', 'N/A')}\n"
            output_text += f"Original MAC: {result.get('original_mac', 'N/A')}\n"
            output_text += f"Base URL: {result.get('base_url', 'N/A')}\n"
            output_text += f"Username: {result.get('username', 'Not extracted')}\n"
            output_text += f"Password: {result.get('password', 'Not extracted')}\n"
            output_text += f"Converted URL: {result.get('converted_url', 'Not available')}\n"
            output_text += f"Message: {result.get('message', 'No message')}\n"

            self.portal_output.clear()
            self.portal_output.setPlainText(output_text)

        except Exception as e:
            logger.error(f"Error displaying portal conversion results: {e}")

    def on_portal_conversion_error(self, error_msg):
        """Handle portal conversion error"""
        self.portal_output.clear()
        self.portal_output.setPlainText(f"Error: {error_msg}")

    def on_portal_conversion_finished(self):
        """Handle portal conversion thread finished"""
        self.portal_progress.setVisible(False)
        self.convert_portal_btn.setEnabled(True)

    def setup_proxy_grabber(self, layout):
        """Setup proxy grabber section"""
        # Proxy Grabber Group
        proxy_group = QGroupBox("🌐 Proxy Grabber")
        proxy_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 14px;
                color: {Colors.TEXT};
                border: 2px solid {Colors.PRIMARY};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        proxy_layout = QVBoxLayout(proxy_group)
        proxy_layout.setSpacing(10)

        # Description
        desc_label = QLabel("Grab free proxies from various sources (Auto-saved to proxies/ folder)")
        desc_label.setStyleSheet(f"color: {Colors.TEXT}; font-size: 12px; padding: 5px;")
        proxy_layout.addWidget(desc_label)

        # Proxy type selection
        type_layout = QHBoxLayout()
        type_label = QLabel("Proxy Type:")
        type_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold;")
        self.proxy_type_combo = QComboBox()
        self.proxy_type_combo.addItems(["HTTP", "HTTPS", "SOCKS4", "SOCKS5"])
        self.proxy_type_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: white;
                border: 2px solid {Colors.PRIMARY};
                border-radius: 4px;
                padding: 5px;
                font-size: 12px;
                min-width: 100px;
            }}
        """)
        type_layout.addWidget(type_label)
        type_layout.addWidget(self.proxy_type_combo)
        type_layout.addStretch()
        proxy_layout.addLayout(type_layout)

        # Grab button and progress
        button_layout = QHBoxLayout()
        self.grab_proxies_btn = QPushButton("🔍 Grab Proxies")
        self.grab_proxies_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.WARNING};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {Colors.WARNING}DD;
            }}
        """)
        self.grab_proxies_btn.clicked.connect(self.grab_proxies)

        # Save proxies button
        self.save_proxies_btn = QPushButton("💾 Save Proxies")
        self.save_proxies_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.PRIMARY};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {Colors.PRIMARY}DD;
            }}
        """)
        self.save_proxies_btn.clicked.connect(self.save_proxies)
        self.save_proxies_btn.setEnabled(False)  # Initially disabled

        self.proxy_progress = QProgressBar()
        self.proxy_progress.setVisible(False)
        self.proxy_progress.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {Colors.PRIMARY};
                border-radius: 5px;
                text-align: center;
            }}
            QProgressBar::chunk {{
                background-color: {Colors.SUCCESS};
                border-radius: 3px;
            }}
        """)

        # Status label
        self.proxy_status_label = QLabel("")
        self.proxy_status_label.setStyleSheet(f"color: {Colors.TEXT}; font-size: 11px;")

        button_layout.addWidget(self.grab_proxies_btn)
        button_layout.addWidget(self.save_proxies_btn)
        button_layout.addWidget(self.proxy_progress)
        button_layout.addStretch()
        proxy_layout.addLayout(button_layout)
        proxy_layout.addWidget(self.proxy_status_label)

        # Output area
        self.proxy_output = QTextEdit()
        self.proxy_output.setMaximumHeight(150)
        self.proxy_output.setStyleSheet(f"""
            QTextEdit {{
                background-color: {Colors.SURFACE};
                border: 1px solid {Colors.PRIMARY};
                border-radius: 4px;
                padding: 8px;
                font-family: monospace;
                font-size: 11px;
                color: {Colors.TEXT};
            }}
        """)
        self.proxy_output.setPlaceholderText("Grabbed proxies will appear here...")
        proxy_layout.addWidget(self.proxy_output)

        layout.addWidget(proxy_group)

    def grab_proxies(self):
        """Grab proxies from various sources"""
        try:
            proxy_type = self.proxy_type_combo.currentText()

            # Show progress
            self.proxy_progress.setVisible(True)
            self.proxy_progress.setValue(0)
            self.grab_proxies_btn.setEnabled(False)
            self.proxy_status_label.setText("Starting proxy grabber...")

            # Create and start proxy grabber thread
            self.proxy_grabber_thread = ProxyGrabberThread(proxy_type)
            self.proxy_grabber_thread.progress_updated.connect(self.proxy_progress.setValue)
            self.proxy_grabber_thread.status_updated.connect(self.proxy_status_label.setText)
            self.proxy_grabber_thread.proxies_found.connect(self.on_proxies_found)
            self.proxy_grabber_thread.error_occurred.connect(self.on_proxy_grabber_error)
            self.proxy_grabber_thread.finished.connect(self.on_proxy_grabber_finished)
            self.proxy_grabber_thread.start()

        except Exception as e:
            logger.error(f"Error starting proxy grabber: {e}")
            QMessageBox.warning(self, "Error", f"Failed to start proxy grabber: {str(e)}")
            self.on_proxy_grabber_finished()

    def on_proxies_found(self, proxies):
        """Handle proxies found"""
        try:
            if proxies:
                # Store proxies for saving
                self.grabbed_proxies = proxies
                proxy_type = self.proxy_type_combo.currentText()

                # Display proxies
                self.proxy_output.clear()
                self.proxy_output.setPlainText('\n'.join(proxies))
                self.proxy_status_label.setText(f"✅ Found {len(proxies)} proxies")

                # Enable save button
                self.save_proxies_btn.setEnabled(True)

                # Auto-save proxies
                self.auto_save_proxies(proxies, proxy_type)
            else:
                self.proxy_output.clear()
                self.proxy_output.setPlainText("No proxies found")
                self.proxy_status_label.setText("❌ No proxies found")
                self.save_proxies_btn.setEnabled(False)

        except Exception as e:
            logger.error(f"Error displaying proxies: {e}")

    def on_proxy_grabber_error(self, error_msg):
        """Handle proxy grabber error"""
        self.proxy_output.clear()
        self.proxy_output.setPlainText(f"Error: {error_msg}")
        self.proxy_status_label.setText(f"❌ Error: {error_msg}")

    def on_proxy_grabber_finished(self):
        """Handle proxy grabber thread finished"""
        self.proxy_progress.setVisible(False)
        self.grab_proxies_btn.setEnabled(True)

    def auto_save_proxies(self, proxies, proxy_type):
        """Automatically save grabbed proxies to a file"""
        try:
            # Create proxies directory if it doesn't exist
            if not os.path.exists("proxies"):
                os.makedirs("proxies")

            # Generate filename with timestamp and proxy type
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"proxies/{proxy_type.lower()}_proxies_{len(proxies)}_{timestamp}.txt"

            # Save proxies to file
            with open(filename, 'w') as f:
                f.write('\n'.join(proxies))

            logger.info(f"Auto-saved {len(proxies)} {proxy_type} proxies to {filename}")

            # Update placeholder text to show save location
            self.proxy_output.setPlaceholderText(f"Grabbed {proxy_type} proxies auto-saved to: {filename}")

        except Exception as e:
            logger.error(f"Error auto-saving proxies: {e}")

    def save_proxies(self):
        """Manually save grabbed proxies to a custom location"""
        try:
            if not hasattr(self, 'grabbed_proxies') or not self.grabbed_proxies:
                QMessageBox.warning(self, "No Proxies", "No proxies to save. Grab some first!")
                return

            # Get proxy type and timestamp
            proxy_type = self.proxy_type_combo.currentText()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"{proxy_type.lower()}_proxies_{len(self.grabbed_proxies)}_{timestamp}.txt"

            # Get save location from user
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Save Proxies",
                default_filename,
                "Text Files (*.txt);;All Files (*)"
            )

            if file_path:
                # Save proxies to selected file
                with open(file_path, 'w') as f:
                    f.write('\n'.join(self.grabbed_proxies))

                QMessageBox.information(
                    self,
                    "Saved Successfully",
                    f"Saved {len(self.grabbed_proxies)} {proxy_type} proxies to:\n{file_path}"
                )
                logger.info(f"Manually saved {len(self.grabbed_proxies)} {proxy_type} proxies to {file_path}")

        except Exception as e:
            logger.error(f"Error saving proxies: {e}")
            QMessageBox.warning(self, "Error", f"Failed to save proxies: {str(e)}")
