import re
import random

class ProxyHandler:
    PROXY_REGEX = re.compile(
        r"^(?:(?P<protocol>http|socks4|socks5)://)?"
        r"(?:([^:]+):([^@]+)@)?"
        r"(?P<ip>[^:]+):"
        r"(?P<port>\d+)$"
    )

    def __init__(self):
        self.proxies = []
        self.current_index = 0
        self.rotate_proxies = True

    def load_proxies(self, file_path):
        try:
            with open(file_path, 'r') as f:
                self.proxies = [self.parse_proxy(line.strip()) for line in f if line.strip()]
            return len(self.proxies)
        except Exception as e:
            raise Exception(f"Proxy load failed: {str(e)}")

    def parse_proxy(self, proxy_str):
        match = self.PROXY_REGEX.match(proxy_str)
        if not match:
            raise ValueError(f"Invalid proxy format: {proxy_str}")

        groups = match.groupdict()
        return {
            'protocol': groups['protocol'] or 'http',  # Default to HTTP
            'ip': groups['ip'],
            'port': groups['port'],
            'auth': (match.group(2), match.group(3)) if match.group(2) else None
        }

    def get_next_proxy(self):
        if not self.proxies:
            return None

        if self.rotate_proxies:
            proxy = self.proxies[self.current_index]
            self.current_index = (self.current_index + 1) % len(self.proxies)
        else:
            proxy = random.choice(self.proxies)

        auth = f"{proxy['auth'][0]}:{proxy['auth'][1]}@" if proxy['auth'] else ""
        return {
            'http': f"{proxy['protocol']}://{auth}{proxy['ip']}:{proxy['port']}",
            'https': f"{proxy['protocol']}://{auth}{proxy['ip']}:{proxy['port']}"
        }

    def get_proxy(self):
        """Alias for get_next_proxy() for compatibility"""
        return self.get_next_proxy()

    def get_proxy_string(self):
        """Get proxy as a simple string format for single proxy usage"""
        if not self.proxies:
            return None

        if self.rotate_proxies:
            proxy = self.proxies[self.current_index]
            self.current_index = (self.current_index + 1) % len(self.proxies)
        else:
            proxy = random.choice(self.proxies)

        auth = f"{proxy['auth'][0]}:{proxy['auth'][1]}@" if proxy['auth'] else ""
        return f"{proxy['protocol']}://{auth}{proxy['ip']}:{proxy['port']}"