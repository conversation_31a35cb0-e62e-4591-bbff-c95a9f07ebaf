from PyQt6.QtGui import QColor, QPalette
from PyQt6.QtCore import Qt

class Colors:
    PRIMARY = "#2196F3"
    SUCCESS = "#4CAF50"
    WARNING = "#FFC107"
    ERROR = "#F44336"
    BACKGROUND = "#F5F5F5"
    SURFACE = "#FFFFFF"
    TEXT = "#212121"

def set_button_style(button, color):
    button.setStyleSheet(f"""
        QPushButton {{
            background-color: {color};
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }}
        QPushButton:hover {{
            background-color: {color}DD;
        }}
        QPushButton:pressed {{
            background-color: {color}AA;
        }}
        QPushButton:disabled {{
            background-color: #CCCCCC;
        }}
    """)