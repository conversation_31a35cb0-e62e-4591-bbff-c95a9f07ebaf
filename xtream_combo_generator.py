#!/usr/bin/env python3
"""
Xtream Combo Generator Module
Enhanced combo generator for Xtream checker with PyQt6 support
"""

import random
import string
import os
import re
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QSpinBox, QComboBox, QLineEdit, QPushButton,
                            QGroupBox, QMessageBox)
from PyQt6.QtCore import Qt

try:
    import names
    NAMES_AVAILABLE = True
except ImportError:
    NAMES_AVAILABLE = False

class XtreamComboGeneratorDialog(QDialog):
    """Enhanced combo generator dialog for Xtream checker"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Xtream Combo Generator")
        self.setGeometry(200, 200, 450, 750)
        self.setModal(True)
        self.init_ui()
        self.apply_styles()

    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout()

        # Number of combos
        num_layout = QHBoxLayout()
        num_label = QLabel("Number of Combos:")
        self.num_combos = QSpinBox()
        self.num_combos.setRange(1, 1000000)
        self.num_combos.setValue(1000)
        num_layout.addWidget(num_label)
        num_layout.addWidget(self.num_combos)
        layout.addLayout(num_layout)

        # Username options group
        username_group = QGroupBox("Username Options")
        username_layout = QVBoxLayout()

        self.username_type = QComboBox()
        username_options = ["Random Letters", "Random Numbers", "Mixed", "Based on Example"]
        if NAMES_AVAILABLE:
            username_options.insert(0, "Random Names")
        self.username_type.addItems(username_options)
        username_layout.addWidget(self.username_type)

        # Username length
        length_layout = QHBoxLayout()
        length_label = QLabel("Length (for non-name options):")
        self.username_length_min = QSpinBox()
        self.username_length_min.setRange(2, 20)
        self.username_length_min.setValue(6)
        self.username_length_max = QSpinBox()
        self.username_length_max.setRange(2, 20)
        self.username_length_max.setValue(12)
        length_layout.addWidget(length_label)
        length_layout.addWidget(self.username_length_min)
        length_layout.addWidget(QLabel("to"))
        length_layout.addWidget(self.username_length_max)
        username_layout.addLayout(length_layout)

        # Username prefix/suffix
        prefix_suffix_layout = QHBoxLayout()
        self.username_prefix = QLineEdit()
        self.username_prefix.setPlaceholderText("Prefix (optional)")
        self.username_suffix = QLineEdit()
        self.username_suffix.setPlaceholderText("Suffix (optional)")
        prefix_suffix_layout.addWidget(self.username_prefix)
        prefix_suffix_layout.addWidget(self.username_suffix)
        username_layout.addLayout(prefix_suffix_layout)

        username_group.setLayout(username_layout)
        layout.addWidget(username_group)

        # Password options group
        password_group = QGroupBox("Password Options")
        password_layout = QVBoxLayout()

        self.password_type = QComboBox()
        self.password_type.addItems([
            "Random Letters", "Random Numbers", "Mixed",
            "Include Special Characters", "Based on Example"
        ])
        password_layout.addWidget(self.password_type)

        # Password length
        pass_length_layout = QHBoxLayout()
        pass_length_label = QLabel("Length:")
        self.password_length_min = QSpinBox()
        self.password_length_min.setRange(2, 20)
        self.password_length_min.setValue(6)
        self.password_length_max = QSpinBox()
        self.password_length_max.setRange(2, 20)
        self.password_length_max.setValue(12)
        pass_length_layout.addWidget(pass_length_label)
        pass_length_layout.addWidget(self.password_length_min)
        pass_length_layout.addWidget(QLabel("to"))
        pass_length_layout.addWidget(self.password_length_max)
        password_layout.addLayout(pass_length_layout)

        # Password prefix/suffix
        prefix_suffix_layout_pass = QHBoxLayout()
        self.password_prefix = QLineEdit()
        self.password_prefix.setPlaceholderText("Prefix (optional)")
        self.password_suffix = QLineEdit()
        self.password_suffix.setPlaceholderText("Suffix (optional)")
        prefix_suffix_layout_pass.addWidget(self.password_prefix)
        prefix_suffix_layout_pass.addWidget(self.password_suffix)
        password_layout.addLayout(prefix_suffix_layout_pass)

        # Exclude characters
        exclude_layout = QHBoxLayout()
        self.exclude_chars = QLineEdit()
        self.exclude_chars.setPlaceholderText("Characters to exclude")
        exclude_layout.addWidget(QLabel("Exclude:"))
        exclude_layout.addWidget(self.exclude_chars)
        password_layout.addLayout(exclude_layout)

        password_group.setLayout(password_layout)
        layout.addWidget(password_group)

        # Example input group
        example_group = QGroupBox("Example Pattern (for 'Based on Example' options)")
        example_layout = QVBoxLayout()

        self.example_input = QLineEdit()
        self.example_input.setPlaceholderText("Enter example like 'user123:pass456'")
        example_layout.addWidget(self.example_input)

        example_group.setLayout(example_layout)
        layout.addWidget(example_group)

        # Buttons
        button_layout = QHBoxLayout()

        self.generate_btn = QPushButton("Generate Combos")
        self.generate_btn.clicked.connect(self.generate_combos)
        button_layout.addWidget(self.generate_btn)

        self.close_btn = QPushButton("Close")
        self.close_btn.setObjectName("close_btn")
        self.close_btn.clicked.connect(self.close)
        button_layout.addWidget(self.close_btn)

        layout.addLayout(button_layout)

        # Status label
        self.status_label = QLabel("")
        self.status_label.setObjectName("status_label")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)

        self.setLayout(layout)

    def apply_styles(self):
        """Apply light theme styling for better visibility"""
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
                color: #333333;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #f8f9fa;
                color: #333333;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
                font-weight: bold;
            }
            QLabel {
                color: #333333;
                font-size: 12px;
                font-weight: normal;
            }
            QLineEdit, QSpinBox, QComboBox {
                background-color: #ffffff;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 6px;
                color: #333333;
                font-size: 12px;
            }
            QLineEdit:focus, QSpinBox:focus, QComboBox:focus {
                border: 2px solid #4a90e2;
                background-color: #f0f8ff;
            }
            QComboBox::drop-down {
                border: none;
                background-color: #f8f9fa;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666;
                margin-right: 5px;
            }
            QPushButton {
                background-color: #4a90e2;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
                min-height: 20px;
            }
            QPushButton:hover {
                background-color: #357abd;
            }
            QPushButton:pressed {
                background-color: #2968a3;
            }
            QPushButton#close_btn {
                background-color: #6c757d;
            }
            QPushButton#close_btn:hover {
                background-color: #5a6268;
            }
            QLabel#status_label {
                color: #2c3e50;
                font-size: 13px;
                font-weight: bold;
                padding: 10px;
                background-color: #e8f4fd;
                border: 1px solid #bee5eb;
                border-radius: 4px;
                margin: 5px;
            }
        """)

    def generate_random_string(self, length, chars):
        """Generate random string with specified characters"""
        exclude_chars = self.exclude_chars.text()
        chars = ''.join(c for c in chars if c not in exclude_chars)
        if not chars:
            chars = string.ascii_letters  # Fallback if all chars excluded
        return ''.join(random.choice(chars) for _ in range(length))

    def generate_based_on_example(self, example):
        """Generate username/password based on example pattern"""
        if ':' not in example:
            return "", ""

        username, password = example.split(':', 1)

        # Replace digits with random digits, letters with random letters
        generated_username = ''.join(
            random.choice(string.digits) if c.isdigit() else
            random.choice(string.ascii_letters) if c.isalpha() else c
            for c in username
        )

        generated_password = ''.join(
            random.choice(string.digits) if c.isdigit() else
            random.choice(string.ascii_letters) if c.isalpha() else
            random.choice(string.punctuation) if c in string.punctuation else c
            for c in password
        )

        return generated_username, generated_password

    def generate_username(self):
        """Generate username based on selected options"""
        username_type = self.username_type.currentText()
        length = random.randint(self.username_length_min.value(), self.username_length_max.value())

        if username_type == "Random Names" and NAMES_AVAILABLE:
            import names
            username = f"{names.get_first_name().lower()}.{names.get_last_name().lower()}"
        elif username_type == "Random Letters":
            username = self.generate_random_string(length, string.ascii_letters)
        elif username_type == "Random Numbers":
            username = self.generate_random_string(length, string.digits)
        elif username_type == "Mixed":
            username = self.generate_random_string(length, string.ascii_letters + string.digits)
        elif username_type == "Based on Example":
            example = self.example_input.text()
            if example and ':' in example:
                username = self.generate_based_on_example(example)[0]
            else:
                username = self.generate_random_string(length, string.ascii_letters + string.digits)
        else:
            username = self.generate_random_string(length, string.ascii_letters + string.digits)

        return f"{self.username_prefix.text()}{username}{self.username_suffix.text()}"

    def generate_password(self):
        """Generate password based on selected options"""
        password_type = self.password_type.currentText()
        length = random.randint(self.password_length_min.value(), self.password_length_max.value())

        if password_type == "Random Letters":
            password = self.generate_random_string(length, string.ascii_letters)
        elif password_type == "Random Numbers":
            password = self.generate_random_string(length, string.digits)
        elif password_type == "Include Special Characters":
            chars = string.ascii_letters + string.digits + "!@#$%^&*"
            password = self.generate_random_string(length, chars)
        elif password_type == "Based on Example":
            example = self.example_input.text()
            if example and ':' in example:
                password = self.generate_based_on_example(example)[1]
            else:
                password = self.generate_random_string(length, string.ascii_letters + string.digits)
        else:  # Mixed
            password = self.generate_random_string(length, string.ascii_letters + string.digits)

        return f"{self.password_prefix.text()}{password}{self.password_suffix.text()}"

    def generate_combos(self):
        """Generate and save combos to file"""
        try:
            num_combos = self.num_combos.value()
            combos = set()

            # Generate unique combos
            attempts = 0
            max_attempts = num_combos * 10  # Prevent infinite loop

            while len(combos) < num_combos and attempts < max_attempts:
                username = self.generate_username()
                password = self.generate_password()

                if username and password:
                    combos.add(f"{username}:{password}")

                attempts += 1

            if not combos:
                QMessageBox.warning(self, "Error", "No combos generated. Please check your settings.")
                return

            # Ensure hits directory exists
            os.makedirs("hits", exist_ok=True)

            # Save combos to file
            filename = f"hits/generated_combos_{len(combos)}.txt"
            with open(filename, "w", encoding="utf-8") as f:
                for combo in sorted(combos):
                    f.write(f"{combo}\n")

            self.status_label.setText(f"✅ Generated {len(combos)} combos saved to {filename}")

            QMessageBox.information(
                self,
                "Success",
                f"Successfully generated {len(combos)} unique combos!\n\nSaved to: {filename}"
            )

        except Exception as e:
            error_msg = f"Error generating combos: {str(e)}"
            self.status_label.setText(f"❌ {error_msg}")
            QMessageBox.critical(self, "Error", error_msg)
