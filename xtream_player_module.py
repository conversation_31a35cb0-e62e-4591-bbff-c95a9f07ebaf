#!/usr/bin/env python3
"""
Xtream Player Module - Full Implementation
Xtream API player for browsing and playing Xtream content with M3U support
Based on Xtream Player.py with VLC integration
"""

import sys
import logging
import requests
import re
import os
import time
from urllib.parse import quote
from PyQt6.QtCore import QThread, pyqtSignal, Qt
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QListWidget, QListWidgetItem, QTabWidget, QProgressBar, QMessageBox,
    QSplitter, QTextEdit, QComboBox, QFrame, QStackedWidget, QCheckBox
)
from PyQt6.QtGui import QFont
from styles import Colors

# VLC imports
try:
    import vlc
    VLC_AVAILABLE = True
except ImportError:
    VLC_AVAILABLE = False
    print("VLC not available - external player will be used")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Custom User Agent (same as Xtream Player.py)
CUSTOM_USER_AGENT = "Mozilla/5.0 (QtEmbedded; U; Linux; C) AppleWebKit/533.3 (KHTML, like Gecko) MAG200 stbapp ver: 2 rev: 250 Safari/533.3"

class XtreamPlayerThread(QThread):
    """Thread for handling Xtream API operations (based on Xtream Player.py)"""
    categories_loaded = pyqtSignal(dict)
    channels_loaded = pyqtSignal(list)
    series_info_loaded = pyqtSignal(dict)
    progress_updated = pyqtSignal(int)
    error_occurred = pyqtSignal(str)
    status_updated = pyqtSignal(str)
    playlist_loaded = pyqtSignal(dict)

    def __init__(self, server_url=None, username=None, password=None, operation="categories",
                 category_type=None, m3u_url=None, category_id=None, series_id=None):
        super().__init__()
        self.server_url = server_url
        self.username = username
        self.password = password
        self.operation = operation
        self.category_type = category_type
        self.m3u_url = m3u_url
        self.category_id = category_id
        self.series_id = series_id

    def run(self):
        try:
            if self.operation == "load_m3u":
                self.load_m3u_playlist()
            elif self.operation == "categories":
                self.load_categories()
            elif self.operation == "channels":
                self.load_channels()
            elif self.operation == "series_info":
                self.load_series_info()
            else:
                self.error_occurred.emit(f"Unknown operation: {self.operation}")

        except Exception as e:
            logger.error(f"XtreamPlayerThread error: {e}")
            self.error_occurred.emit(str(e))

    def load_m3u_playlist(self):
        """Load M3U playlist (same logic as Xtream Player.py)"""
        try:
            self.status_updated.emit("Loading M3U playlist...")
            self.progress_updated.emit(10)

            # Extract credentials from M3U URL if needed
            if self.m3u_url and "get.php" in self.m3u_url:
                pattern = r'(http[s]?://[^/]+)/get\.php\?username=([^&]*)&password=([^&]*)&type=(m3u_plus|m3u|&output=m3u8)'
                match = re.match(pattern, self.m3u_url)
                if match:
                    self.server_url = match.group(1)
                    self.username = match.group(2)
                    self.password = match.group(3)
                    logger.info(f"Extracted credentials from M3U URL: {self.server_url}")

            self.status_updated.emit("Authenticating...")
            self.progress_updated.emit(30)

            # Test authentication
            auth_url = f"{self.server_url}/player_api.php"
            params = {
                'username': self.username,
                'password': self.password,
                'action': 'user_info'
            }

            headers = {'User-Agent': CUSTOM_USER_AGENT}
            response = requests.get(auth_url, params=params, headers=headers, timeout=15)
            response.raise_for_status()
            user_info = response.json()

            if user_info.get('user_info', {}).get('auth') != 1:
                raise Exception("Authentication failed - Invalid credentials")

            self.status_updated.emit("Authentication successful!")
            self.progress_updated.emit(50)

            # Load categories
            self.status_updated.emit("Loading categories...")
            self.progress_updated.emit(70)

            categories = {
                'Live': self.fetch_categories('live'),
                'Movies': self.fetch_categories('vod'),
                'Series': self.fetch_categories('series')
            }

            self.status_updated.emit("✅ Playlist loaded successfully!")
            self.progress_updated.emit(100)
            self.playlist_loaded.emit({
                'server': self.server_url,
                'username': self.username,
                'password': self.password,
                'categories': categories
            })

        except Exception as e:
            logger.error(f"Failed to load M3U playlist: {e}")
            self.error_occurred.emit(f"Failed to load M3U playlist: {str(e)}")

    def fetch_categories(self, category_type):
        """Fetch categories for a specific type (same logic as Xtream Player.py)"""
        try:
            if category_type == 'live':
                action = 'get_live_categories'
            elif category_type == 'vod':
                action = 'get_vod_categories'
            elif category_type == 'series':
                action = 'get_series_categories'
            else:
                return []

            url = f"{self.server_url}/player_api.php"
            params = {
                'username': self.username,
                'password': self.password,
                'action': action
            }

            headers = {'User-Agent': CUSTOM_USER_AGENT}
            response = requests.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()

            categories = response.json()
            if isinstance(categories, list):
                return categories
            else:
                logger.warning(f"Unexpected categories format for {category_type}: {type(categories)}")
                return []

        except Exception as e:
            logger.error(f"Failed to fetch {category_type} categories: {e}")
            return []

    def load_categories(self):
        """Load categories for authenticated connection"""
        try:
            self.status_updated.emit("Loading categories...")
            self.progress_updated.emit(30)

            categories = {
                'Live': self.fetch_categories('live'),
                'Movies': self.fetch_categories('vod'),
                'Series': self.fetch_categories('series')
            }

            self.status_updated.emit("✅ Categories loaded!")
            self.progress_updated.emit(100)
            self.categories_loaded.emit(categories)

        except Exception as e:
            logger.error(f"Failed to load categories: {e}")
            self.error_occurred.emit(f"Failed to load categories: {str(e)}")

    def load_channels(self):
        """Load channels for a specific category"""
        try:
            self.status_updated.emit("Loading channels...")
            self.progress_updated.emit(30)

            if self.category_type == 'live':
                action = 'get_live_streams'
                stream_type = 'live'
            elif self.category_type == 'vod':
                action = 'get_vod_streams'
                stream_type = 'movie'
            elif self.category_type == 'series':
                action = 'get_series'
                stream_type = 'series'
            else:
                raise Exception(f"Unknown category type: {self.category_type}")

            url = f"{self.server_url}/player_api.php"
            params = {
                'username': self.username,
                'password': self.password,
                'action': action
            }

            if self.category_id:
                params['category_id'] = self.category_id

            headers = {'User-Agent': CUSTOM_USER_AGENT}
            response = requests.get(url, params=params, headers=headers, timeout=15)
            response.raise_for_status()

            channels = response.json()
            if not isinstance(channels, list):
                raise Exception("Expected a list of channels")

            # Add stream URLs (same logic as Xtream Player.py)
            for channel in channels:
                stream_id = channel.get("stream_id")
                container_extension = channel.get("container_extension", "m3u8")
                if stream_id:
                    channel["url"] = f"{self.server_url}/{stream_type}/{self.username}/{self.password}/{stream_id}.{container_extension}"
                else:
                    channel["url"] = None

            self.status_updated.emit(f"✅ Loaded {len(channels)} channels!")
            self.progress_updated.emit(100)
            self.channels_loaded.emit(channels)

        except Exception as e:
            logger.error(f"Failed to load channels: {e}")
            self.error_occurred.emit(f"Failed to load channels: {str(e)}")

    def load_series_info(self):
        """Load series information including seasons and episodes"""
        try:
            self.status_updated.emit("Loading series information...")
            self.progress_updated.emit(30)

            if not self.series_id:
                raise Exception("Series ID is required")

            url = f"{self.server_url}/player_api.php"
            params = {
                'username': self.username,
                'password': self.password,
                'action': 'get_series_info',
                'series_id': self.series_id
            }

            headers = {'User-Agent': CUSTOM_USER_AGENT}
            response = requests.get(url, params=params, headers=headers, timeout=15)
            response.raise_for_status()

            series_info = response.json()
            if not isinstance(series_info, dict):
                raise Exception("Expected series info as dictionary")

            self.status_updated.emit("✅ Series info loaded!")
            self.progress_updated.emit(100)
            self.series_info_loaded.emit(series_info)

        except Exception as e:
            logger.error(f"Failed to load series info: {e}")
            self.error_occurred.emit(f"Failed to load series info: {str(e)}")

class XtreamPlayerWidget(QWidget):
    """Xtream player widget with full implementation (same design as Stalker player)"""

    def __init__(self):
        super().__init__()
        self.server_url = None
        self.username = None
        self.password = None
        self.categories = {}
        self.current_channels = []
        self.current_view = "categories"
        self.current_category = None

        # VLC setup
        self.vlc_instance = None
        self.vlc_media_player = None
        self.vlc_widget = None

        self.init_ui()
        self.setup_vlc()

    def init_ui(self):
        """Initialize the user interface using design.py layout structure"""
        # Main container - horizontal split like design.py
        main_container = QSplitter(Qt.Orientation.Horizontal)
        main_container.setStyleSheet(f"""
            QSplitter::handle {{
                background-color: {Colors.PRIMARY};
                width: 3px;
            }}
        """)

        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.addWidget(main_container)

        # Left panel (weight=1) - Connection fields and content lists
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setSpacing(5)
        left_layout.setContentsMargins(5, 5, 5, 5)

        # Connection section (compact design like Stalker player)
        connection_frame = QFrame()
        connection_frame.setStyleSheet(f"""
            QFrame {{
                border: 2px solid {Colors.PRIMARY};
                border-radius: 6px;
                background-color: {Colors.SURFACE};
                padding: 5px;
                margin-bottom: 3px;
            }}
        """)
        connection_layout = QVBoxLayout(connection_frame)
        connection_layout.setSpacing(3)  # Reduced spacing for compact layout

        # Connection method selection
        method_layout = QHBoxLayout()
        method_label = QLabel("Connection Method:")
        method_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold;")
        self.method_combo = QComboBox()
        self.method_combo.addItems(["Manual Entry", "M3U URL"])
        self.method_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: {Colors.BACKGROUND};
                color: {Colors.TEXT};
                border: 1px solid {Colors.PRIMARY};
                border-radius: 4px;
                padding: 5px;
                min-width: 120px;
            }}
            QComboBox::drop-down {{
                border: none;
            }}
            QComboBox::down-arrow {{
                image: none;
                border: none;
            }}
        """)
        self.method_combo.currentTextChanged.connect(self.on_method_changed)
        method_layout.addWidget(method_label)
        method_layout.addWidget(self.method_combo)
        method_layout.addStretch()
        connection_layout.addLayout(method_layout)

        # Stacked widget for different input methods
        self.input_stack = QStackedWidget()
        self.input_stack.setMinimumHeight(120)  # Increased minimum height for better field visibility

        # Manual entry widget
        manual_widget = QWidget()
        manual_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {Colors.SURFACE};
                border-radius: 4px;
                padding: 5px;
            }}
        """)
        manual_layout = QVBoxLayout(manual_widget)
        manual_layout.setSpacing(8)  # Increased spacing for better visibility
        manual_layout.setContentsMargins(10, 10, 10, 10)  # Increased margins for better visibility

        # Server URL
        server_layout = QHBoxLayout()
        server_label = QLabel("Server URL:")
        server_label.setMinimumWidth(90)
        server_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold; font-size: 12px;")
        self.server_input = QLineEdit()
        self.server_input.setPlaceholderText("http://server.com:8080")
        self.server_input.setMinimumHeight(30)  # Increased height
        self.server_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {Colors.BACKGROUND};
                color: {Colors.TEXT};
                border: 2px solid {Colors.PRIMARY};
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                min-height: 20px;
            }}
            QLineEdit:focus {{
                border: 2px solid {Colors.SUCCESS};
                background-color: {Colors.SURFACE};
            }}
        """)
        server_layout.addWidget(server_label)
        server_layout.addWidget(self.server_input)
        manual_layout.addLayout(server_layout)

        # Username
        username_layout = QHBoxLayout()
        username_label = QLabel("Username:")
        username_label.setMinimumWidth(90)
        username_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold; font-size: 12px;")
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("username")
        self.username_input.setMinimumHeight(30)  # Increased height
        self.username_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {Colors.BACKGROUND};
                color: {Colors.TEXT};
                border: 2px solid {Colors.PRIMARY};
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                min-height: 20px;
            }}
            QLineEdit:focus {{
                border: 2px solid {Colors.SUCCESS};
                background-color: {Colors.SURFACE};
            }}
        """)
        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_input)
        manual_layout.addLayout(username_layout)

        # Password
        password_layout = QHBoxLayout()
        password_label = QLabel("Password:")
        password_label.setMinimumWidth(90)
        password_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold; font-size: 12px;")
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("password")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setMinimumHeight(30)  # Increased height
        self.password_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {Colors.BACKGROUND};
                color: {Colors.TEXT};
                border: 2px solid {Colors.PRIMARY};
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                min-height: 20px;
            }}
            QLineEdit:focus {{
                border: 2px solid {Colors.SUCCESS};
                background-color: {Colors.SURFACE};
            }}
        """)
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)
        manual_layout.addLayout(password_layout)

        self.input_stack.addWidget(manual_widget)

        # M3U URL widget
        m3u_widget = QWidget()
        m3u_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {Colors.SURFACE};
                border-radius: 4px;
                padding: 5px;
            }}
        """)
        m3u_layout = QVBoxLayout(m3u_widget)
        m3u_layout.setSpacing(8)  # Increased spacing for better visibility
        m3u_layout.setContentsMargins(10, 10, 10, 10)  # Increased margins for better visibility

        m3u_url_layout = QHBoxLayout()
        m3u_label = QLabel("M3U URL:")
        m3u_label.setMinimumWidth(90)
        m3u_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold; font-size: 12px;")
        self.m3u_input = QLineEdit()
        self.m3u_input.setPlaceholderText("http://server.com:8080/get.php?username=user&password=pass&type=m3u_plus")
        self.m3u_input.setMinimumHeight(30)  # Increased height
        self.m3u_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {Colors.BACKGROUND};
                color: {Colors.TEXT};
                border: 2px solid {Colors.PRIMARY};
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                min-height: 20px;
            }}
            QLineEdit:focus {{
                border: 2px solid {Colors.SUCCESS};
                background-color: {Colors.SURFACE};
            }}
        """)
        m3u_url_layout.addWidget(m3u_label)
        m3u_url_layout.addWidget(self.m3u_input)
        m3u_layout.addLayout(m3u_url_layout)

        self.input_stack.addWidget(m3u_widget)
        connection_layout.addWidget(self.input_stack)

        # Connect button
        button_layout = QHBoxLayout()
        self.connect_btn = QPushButton("🔗 Connect")
        self.connect_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.SUCCESS};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {Colors.SUCCESS}DD;
            }}
            QPushButton:pressed {{
                background-color: {Colors.SUCCESS}BB;
            }}
            QPushButton:disabled {{
                background-color: #CCCCCC;
                color: #666666;
            }}
        """)
        self.connect_btn.clicked.connect(self.connect_to_server)
        button_layout.addWidget(self.connect_btn)
        button_layout.addStretch()
        connection_layout.addLayout(button_layout)

        # Set size policy to keep connection frame compact and always visible
        from PyQt6.QtWidgets import QSizePolicy
        connection_frame.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        connection_frame.setMinimumHeight(180)  # Increased minimum height for larger fields
        connection_frame.setMaximumHeight(220)  # Increased to accommodate larger fields

        left_layout.addWidget(connection_frame)

        # Content area (like design.py notebook) - moved to left panel
        # Removed "Content" label to save space

        # Content tabs
        self.content_tabs = QTabWidget()
        self.content_tabs.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {Colors.PRIMARY};
                background-color: {Colors.BACKGROUND};
            }}
            QTabBar::tab {{
                background-color: {Colors.SURFACE};
                color: {Colors.TEXT};
                padding: 6px 12px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                font-weight: bold;
                min-width: 60px;
            }}
            QTabBar::tab:selected {{
                background-color: {Colors.PRIMARY};
                color: white;
            }}
            QTabBar::tab:hover {{
                background-color: {Colors.PRIMARY}CC;
            }}
        """)

        # Create content lists for each category
        self.live_list = QListWidget()
        self.movies_list = QListWidget()
        self.series_list = QListWidget()

        # Style the lists (same as Stalker player)
        list_style = f"""
            QListWidget {{
                background-color: {Colors.BACKGROUND};
                color: {Colors.TEXT};
                border: 1px solid {Colors.PRIMARY};
                border-radius: 4px;
                padding: 5px;
                font-size: 11px;
            }}
            QListWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {Colors.SURFACE};
                border-radius: 4px;
                margin: 1px;
            }}
            QListWidget::item:selected {{
                background-color: {Colors.PRIMARY};
                color: white;
            }}
            QListWidget::item:hover {{
                background-color: {Colors.PRIMARY}AA;
                color: white;
            }}
        """

        self.live_list.setStyleSheet(list_style)
        self.movies_list.setStyleSheet(list_style)
        self.series_list.setStyleSheet(list_style)

        # Connect double-click events
        self.live_list.itemDoubleClicked.connect(self.on_item_double_clicked)
        self.movies_list.itemDoubleClicked.connect(self.on_item_double_clicked)
        self.series_list.itemDoubleClicked.connect(self.on_item_double_clicked)

        # Add tabs
        self.content_tabs.addTab(self.live_list, "📺 Live TV")
        self.content_tabs.addTab(self.movies_list, "🎬 Movies")
        self.content_tabs.addTab(self.series_list, "📺 Series")

        # Add content tabs with stretch factor so they take remaining space
        # while keeping connection frame fixed at the top
        left_layout.addWidget(self.content_tabs, 1)  # Stretch factor 1

        # Test button for controls (temporary for debugging)
        self.test_controls_btn = QPushButton("🎮 Show Controls")
        self.test_controls_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.WARNING};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
                font-size: 11px;
            }}
            QPushButton:hover {{
                background-color: {Colors.WARNING}DD;
            }}
        """)
        self.test_controls_btn.clicked.connect(self.test_show_controls)
        left_layout.addWidget(self.test_controls_btn)

        # Add left panel to main container
        main_container.addWidget(left_panel)

        # Right panel (weight=3) - Large video player like design.py
        right_panel = QWidget()
        self.setup_video_player_right(right_panel)
        main_container.addWidget(right_panel)

        # Set proportions like design.py (left=1, right=3)
        main_container.setSizes([300, 900])

        # Progress bar (at bottom of left panel)
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid {Colors.PRIMARY};
                border-radius: 4px;
                text-align: center;
                background-color: {Colors.BACKGROUND};
                color: {Colors.TEXT};
                font-weight: bold;
                max-height: 20px;
            }}
            QProgressBar::chunk {{
                background-color: {Colors.SUCCESS};
                border-radius: 3px;
            }}
        """)
        self.progress_bar.setVisible(False)
        left_layout.addWidget(self.progress_bar)

        # Status label (at bottom of left panel) - REMOVED "Ready to connect" for more space
        self.status_label = QLabel("")
        self.status_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold; padding: 2px; font-size: 11px;")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        left_layout.addWidget(self.status_label)

    def setup_video_player_right(self, right_panel):
        """Setup the large video player for right panel with auto-hide controls"""
        player_layout = QVBoxLayout(right_panel)
        player_layout.setContentsMargins(5, 5, 5, 5)

        # Create a container for video player with overlay controls
        self.video_container = QWidget()
        self.video_container.setStyleSheet("QWidget { background-color: #1a1a1a; }")
        player_layout.addWidget(self.video_container)

        # Use absolute positioning for overlay controls
        container_layout = QVBoxLayout(self.video_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(0)

        # Large VLC Player widget (like design.py video_frame) - NO transparency here
        self.vlc_widget = QWidget()
        self.vlc_widget.setStyleSheet(f"""
            QWidget {{
                background-color: #1a1a1a;
                border: 2px solid {Colors.PRIMARY};
                border-radius: 8px;
                min-height: 500px;
            }}
        """)

        # Enable mouse tracking for auto-hide functionality
        self.vlc_widget.setMouseTracking(True)
        self.video_container.setMouseTracking(True)
        right_panel.setMouseTracking(True)

        container_layout.addWidget(self.vlc_widget)

        # Player controls - Overlay with auto-hide functionality
        self.controls_frame = QFrame(self.video_container)
        self.controls_frame.setStyleSheet(f"""
            QFrame {{
                background-color: rgba(45, 45, 45, 0.9);
                border: 1px solid rgba(0, 123, 255, 0.5);
                border-radius: 25px;
                padding: 4px 8px;
                max-height: 50px;
            }}
        """)

        # Position controls as overlay at bottom center
        self.controls_frame.setFixedSize(160, 50)
        self.controls_frame.move(
            (self.video_container.width() - 160) // 2,
            self.video_container.height() - 70
        )

        # Initially hide controls
        self.controls_frame.hide()

        controls_layout = QHBoxLayout(self.controls_frame)
        controls_layout.setSpacing(8)  # Reduce spacing between buttons
        controls_layout.setContentsMargins(8, 4, 8, 4)

        # Control buttons - Compact and transparent design
        self.play_btn = QPushButton("▶️")
        self.pause_btn = QPushButton("⏸️")
        self.stop_btn = QPushButton("⏹️")

        # Style the control buttons - Smaller and more transparent
        button_style = f"""
            QPushButton {{
                background-color: rgba(0, 123, 255, 0.7);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 20px;
                padding: 4px;
                font-weight: bold;
                font-size: 14px;
                min-width: 40px;
                max-width: 40px;
                min-height: 40px;
                max-height: 40px;
            }}
            QPushButton:hover {{
                background-color: rgba(0, 123, 255, 0.9);
                border: 1px solid rgba(255, 255, 255, 0.5);
            }}
            QPushButton:pressed {{
                background-color: rgba(0, 123, 255, 1.0);
            }}
            QPushButton:disabled {{
                background-color: rgba(204, 204, 204, 0.5);
                color: rgba(102, 102, 102, 0.7);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }}
        """

        self.play_btn.setStyleSheet(button_style)
        self.pause_btn.setStyleSheet(button_style)
        self.stop_btn.setStyleSheet(button_style)

        # Connect control buttons
        self.play_btn.clicked.connect(self.play_video)
        self.pause_btn.clicked.connect(self.pause_video)
        self.stop_btn.clicked.connect(self.stop_video)

        # Initially disable controls
        self.play_btn.setEnabled(False)
        self.pause_btn.setEnabled(False)
        self.stop_btn.setEnabled(False)

        controls_layout.addWidget(self.play_btn)
        controls_layout.addWidget(self.pause_btn)
        controls_layout.addWidget(self.stop_btn)

        # Setup auto-hide timer
        from PyQt6.QtCore import QTimer
        self.hide_timer = QTimer()
        self.hide_timer.timeout.connect(self.hide_controls)
        self.hide_timer.setSingleShot(True)

        # Enable mouse tracking for the video area
        self.video_container.setMouseTracking(True)
        self.vlc_widget.setMouseTracking(True)

        # Install event filters to capture mouse events properly
        self.video_container.installEventFilter(self)
        self.vlc_widget.installEventFilter(self)

    def eventFilter(self, obj, event):
        """Event filter to handle mouse events on video widgets"""
        from PyQt6.QtCore import QEvent

        # Check if the event is from our video widgets
        if obj in [self.video_container, self.vlc_widget]:
            if event.type() == QEvent.Type.Enter:
                # Mouse entered video area
                logger.debug("Xtream Mouse entered video area")
                self.show_controls()
                return False
            elif event.type() == QEvent.Type.Leave:
                # Mouse left video area - start hide timer
                logger.debug("Xtream Mouse left video area")
                self.hide_timer.start(2000)  # Hide after 2 seconds
                return False
            elif event.type() == QEvent.Type.MouseMove:
                # Mouse moved in video area
                logger.debug("Xtream Mouse moved in video area")
                self.show_controls()
                return False

        # Pass the event to the parent class
        return super().eventFilter(obj, event)

    def show_controls(self):
        """Show the overlay controls"""
        if hasattr(self, 'controls_frame'):
            # Position controls at bottom center
            container_width = self.video_container.width()
            container_height = self.video_container.height()
            controls_width = 160
            controls_height = 50

            x = (container_width - controls_width) // 2
            y = container_height - controls_height - 20

            self.controls_frame.move(x, y)
            self.controls_frame.show()
            self.controls_frame.raise_()

            # Stop hide timer if running
            if self.hide_timer.isActive():
                self.hide_timer.stop()

    def hide_controls(self):
        """Hide the overlay controls"""
        if hasattr(self, 'controls_frame'):
            self.controls_frame.hide()

    def test_show_controls(self):
        """Test method to manually show controls for debugging"""
        logger.info("Xtream Test button clicked - attempting to show controls")
        if hasattr(self, 'controls_frame'):
            logger.info(f"Xtream Controls frame exists: {self.controls_frame}")
            logger.info(f"Xtream Controls frame parent: {self.controls_frame.parent()}")
            logger.info(f"Xtream Controls frame visible: {self.controls_frame.isVisible()}")
            logger.info(f"Xtream Video container size: {self.video_container.size()}")

            # Force show controls
            self.show_controls()

            # Also try to show them manually
            self.controls_frame.setVisible(True)
            self.controls_frame.show()
            self.controls_frame.raise_()

            logger.info(f"Xtream After manual show - visible: {self.controls_frame.isVisible()}")
        else:
            logger.error("Xtream Controls frame does not exist!")

    def setup_vlc(self):
        """Setup VLC player (same as Stalker player)"""
        if not VLC_AVAILABLE:
            logger.warning("VLC not available - external player will be used")
            return

        try:
            # Create VLC instance
            self.vlc_instance = vlc.Instance()

            # Create VLC media player
            self.vlc_media_player = self.vlc_instance.media_player_new()

            # Set the VLC widget as the video output
            if hasattr(self.vlc_widget, 'winId'):
                self.vlc_media_player.set_hwnd(self.vlc_widget.winId())

            logger.info("VLC integration setup successful")

        except Exception as e:
            logger.error(f"Failed to setup VLC: {e}")
            self.vlc_instance = None
            self.vlc_media_player = None

    def on_method_changed(self):
        """Handle connection method change"""
        method = self.method_combo.currentText()
        if method == "Manual Entry":
            self.input_stack.setCurrentIndex(0)
        else:  # M3U URL
            self.input_stack.setCurrentIndex(1)

    def connect_to_server(self):
        """Connect to Xtream server (same logic as Xtream Player.py)"""
        try:
            method = self.method_combo.currentText()

            if method == "Manual Entry":
                # Get manual credentials
                self.server_url = self.server_input.text().strip()
                self.username = self.username_input.text().strip()
                self.password = self.password_input.text().strip()

                if not all([self.server_url, self.username, self.password]):
                    QMessageBox.warning(self, "Error", "Please fill in all fields")
                    return

                # Start connection thread
                self.connect_btn.setEnabled(False)
                self.progress_bar.setVisible(True)
                self.progress_bar.setValue(0)
                self.status_label.setText("Connecting...")

                self.connection_thread = XtreamPlayerThread(
                    server_url=self.server_url,
                    username=self.username,
                    password=self.password,
                    operation="categories"
                )

            else:  # M3U URL
                # Get M3U URL
                m3u_url = self.m3u_input.text().strip()

                if not m3u_url:
                    QMessageBox.warning(self, "Error", "Please enter M3U URL")
                    return

                # Start M3U loading thread
                self.connect_btn.setEnabled(False)
                self.progress_bar.setVisible(True)
                self.progress_bar.setValue(0)
                self.status_label.setText("Loading M3U playlist...")

                self.connection_thread = XtreamPlayerThread(
                    m3u_url=m3u_url,
                    operation="load_m3u"
                )

            # Connect signals
            self.connection_thread.categories_loaded.connect(self.on_categories_loaded)
            self.connection_thread.playlist_loaded.connect(self.on_playlist_loaded)
            self.connection_thread.progress_updated.connect(self.progress_bar.setValue)
            self.connection_thread.status_updated.connect(self.status_label.setText)
            self.connection_thread.error_occurred.connect(self.on_connection_error)
            self.connection_thread.finished.connect(self.on_connection_finished)

            # Start the thread
            self.connection_thread.start()

        except Exception as e:
            logger.error(f"Connection error: {e}")
            QMessageBox.critical(self, "Error", f"Connection failed: {str(e)}")
            self.on_connection_finished()

    def on_categories_loaded(self, categories):
        """Handle categories loaded"""
        try:
            self.categories = categories
            self.populate_categories()
            self.status_label.setText("✅ Connected successfully!")

        except Exception as e:
            logger.error(f"Error loading categories: {e}")
            self.status_label.setText("Error loading categories")

    def on_playlist_loaded(self, playlist_data):
        """Handle M3U playlist loaded"""
        try:
            self.server_url = playlist_data['server']
            self.username = playlist_data['username']
            self.password = playlist_data['password']
            self.categories = playlist_data['categories']

            # Update manual fields with extracted credentials
            self.server_input.setText(self.server_url)
            self.username_input.setText(self.username)
            self.password_input.setText(self.password)

            self.populate_categories()
            self.status_label.setText("✅ M3U playlist loaded successfully!")

        except Exception as e:
            logger.error(f"Error loading playlist: {e}")
            self.status_label.setText("Error loading playlist")

    def populate_categories(self):
        """Populate category lists (same logic as Xtream Player.py)"""
        try:
            # Clear existing items
            self.live_list.clear()
            self.movies_list.clear()
            self.series_list.clear()

            # Populate Live TV categories
            live_categories = self.categories.get('Live', [])
            for category in live_categories:
                item = QListWidgetItem(f"📺 {category.get('category_name', 'Unknown')}")
                item.setData(Qt.ItemDataRole.UserRole, category)
                self.live_list.addItem(item)

            # Populate Movies categories
            movie_categories = self.categories.get('Movies', [])
            for category in movie_categories:
                item = QListWidgetItem(f"🎬 {category.get('category_name', 'Unknown')}")
                item.setData(Qt.ItemDataRole.UserRole, category)
                self.movies_list.addItem(item)

            # Populate Series categories
            series_categories = self.categories.get('Series', [])
            for category in series_categories:
                item = QListWidgetItem(f"📺 {category.get('category_name', 'Unknown')}")
                item.setData(Qt.ItemDataRole.UserRole, category)
                self.series_list.addItem(item)

            logger.info(f"Populated categories: {len(live_categories)} Live, {len(movie_categories)} Movies, {len(series_categories)} Series")

        except Exception as e:
            logger.error(f"Error populating categories: {e}")

    def on_item_double_clicked(self, item):
        """Handle item double-click with proper series support"""
        try:
            item_data = item.data(Qt.ItemDataRole.UserRole)
            if not item_data:
                return

            # Check if this is a back button
            if item_data.get("type") == "back":
                self.go_back_to_categories()
                return
            elif item_data.get("type") == "back_to_seasons":
                # Go back to seasons view
                series_info = item_data.get("series_info")
                if series_info:
                    self.on_series_info_loaded(series_info)
                else:
                    self.go_back_to_categories()
                return

            # Determine current tab to understand context
            current_tab = self.content_tabs.currentIndex()

            # Check item type for special handling
            item_type = item_data.get("type")

            if item_type == "season":
                # This is a season - show episodes
                self.show_season_episodes(item_data)
                return
            elif item_type == "episode":
                # This is an episode - play it
                self.play_channel(item_data)
                return

            # Check if this is a content item (has 'url' field)
            if item_data.get("url"):
                # For series tab, handle series differently
                if current_tab == 2:  # Series tab
                    # This is a series - load seasons/episodes instead of playing directly
                    self.handle_series_click(item_data)
                    return
                else:
                    # For live TV and movies, play directly
                    self.play_channel(item_data)
                    return

            # This is a category - load channels for this category
            if current_tab == 0:  # Live TV
                category_type = 'live'
            elif current_tab == 1:  # Movies
                category_type = 'vod'
            else:  # Series
                category_type = 'series'

            # Load channels for this category
            self.load_channels(item_data, category_type)

        except Exception as e:
            logger.error(f"Error handling item double-click: {e}")

    def handle_series_click(self, series_data):
        """Handle series click - load seasons and episodes"""
        try:
            series_name = series_data.get("name", "Unknown Series")
            series_id = series_data.get("series_id") or series_data.get("stream_id")

            if not series_id:
                QMessageBox.warning(self, "Error", "Series ID not found")
                return

            # Show loading
            self.status_label.setText(f"Loading series info for: {series_name}")
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            # Start series info loading thread
            self.series_thread = XtreamPlayerThread(
                server_url=self.server_url,
                username=self.username,
                password=self.password,
                operation="series_info",
                series_id=series_id
            )

            # Connect signals
            self.series_thread.series_info_loaded.connect(self.on_series_info_loaded)
            self.series_thread.progress_updated.connect(self.progress_bar.setValue)
            self.series_thread.status_updated.connect(self.status_label.setText)
            self.series_thread.error_occurred.connect(self.on_series_error)
            self.series_thread.finished.connect(self.on_connection_finished)

            # Start the thread
            self.series_thread.start()

        except Exception as e:
            logger.error(f"Error handling series click: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load series: {str(e)}")

    def on_series_info_loaded(self, series_info):
        """Handle series info loaded - display seasons and episodes"""
        try:
            self.progress_bar.setVisible(False)

            # Get series details
            info = series_info.get('info', {})
            seasons = series_info.get('seasons', [])
            episodes = series_info.get('episodes', {})

            series_name = info.get('name', 'Unknown Series')

            # Clear current list and show series content
            current_list = self.series_list
            current_list.clear()

            # Add back button
            back_item = QListWidgetItem("⬅️ Back to Series")
            back_item.setData(Qt.ItemDataRole.UserRole, {"type": "back"})
            current_list.addItem(back_item)

            # If there are seasons, show them
            if seasons:
                for season in seasons:
                    season_name = season.get('name', f"Season {season.get('season_number', '?')}")
                    season_number = season.get('season_number')

                    # Get episodes for this season
                    season_episodes = episodes.get(str(season_number), []) if season_number else []
                    episode_count = len(season_episodes)

                    item_text = f"📺 {season_name} ({episode_count} episodes)"
                    item = QListWidgetItem(item_text)

                    # Store season data with episodes
                    season_data = season.copy()
                    season_data['episodes'] = season_episodes
                    season_data['series_info'] = series_info
                    season_data['type'] = 'season'

                    item.setData(Qt.ItemDataRole.UserRole, season_data)
                    current_list.addItem(item)

                self.status_label.setText(f"✅ Loaded {len(seasons)} seasons for {series_name}")

            # If no seasons but episodes exist, show episodes directly
            elif episodes:
                all_episodes = []
                for season_episodes in episodes.values():
                    all_episodes.extend(season_episodes)

                for episode in all_episodes:
                    episode_name = episode.get('title', f"Episode {episode.get('episode_num', '?')}")
                    item_text = f"🎬 {episode_name}"
                    item = QListWidgetItem(item_text)

                    # Create episode URL for playback
                    episode_id = episode.get('id')
                    container_extension = episode.get('container_extension', 'mp4')
                    if episode_id:
                        episode['url'] = f"{self.server_url}/series/{self.username}/{self.password}/{episode_id}.{container_extension}"

                    episode['type'] = 'episode'
                    item.setData(Qt.ItemDataRole.UserRole, episode)
                    current_list.addItem(item)

                self.status_label.setText(f"✅ Loaded {len(all_episodes)} episodes for {series_name}")

            else:
                # No seasons or episodes found
                no_content_item = QListWidgetItem("❌ No content available for this series")
                current_list.addItem(no_content_item)
                self.status_label.setText(f"No content found for {series_name}")

        except Exception as e:
            logger.error(f"Error handling series info: {e}")
            QMessageBox.critical(self, "Error", f"Failed to process series info: {str(e)}")

    def on_series_error(self, error_message):
        """Handle series loading error"""
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, "Series Error", f"Failed to load series:\n\n{error_message}")
        self.status_label.setText("Failed to load series")

    def show_season_episodes(self, season_data):
        """Show episodes for a selected season"""
        try:
            episodes = season_data.get('episodes', [])
            season_name = season_data.get('name', 'Unknown Season')

            # Clear current list and show episodes
            current_list = self.series_list
            current_list.clear()

            # Add back button
            back_item = QListWidgetItem("⬅️ Back to Seasons")
            back_item.setData(Qt.ItemDataRole.UserRole, {"type": "back_to_seasons", "series_info": season_data.get('series_info')})
            current_list.addItem(back_item)

            # Add episodes
            for episode in episodes:
                episode_name = episode.get('title', f"Episode {episode.get('episode_num', '?')}")
                item_text = f"🎬 {episode_name}"
                item = QListWidgetItem(item_text)

                # Create episode URL for playback
                episode_id = episode.get('id')
                container_extension = episode.get('container_extension', 'mp4')
                if episode_id:
                    episode['url'] = f"{self.server_url}/series/{self.username}/{self.password}/{episode_id}.{container_extension}"

                episode['type'] = 'episode'
                episode['name'] = episode_name  # Ensure name is set for playback
                item.setData(Qt.ItemDataRole.UserRole, episode)
                current_list.addItem(item)

            self.status_label.setText(f"✅ Loaded {len(episodes)} episodes for {season_name}")

        except Exception as e:
            logger.error(f"Error showing season episodes: {e}")
            QMessageBox.critical(self, "Error", f"Failed to show episodes: {str(e)}")

    def go_back_to_categories(self):
        """Go back to categories view"""
        try:
            if self.categories:
                self.populate_categories()
                self.status_label.setText("✅ Back to categories")

        except Exception as e:
            logger.error(f"Error going back to categories: {e}")

    def load_channels(self, category_data, category_type):
        """Load channels for a category"""
        try:
            category_id = category_data.get('category_id')
            category_name = category_data.get('category_name', 'Unknown')

            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.status_label.setText(f"Loading {category_name}...")

            # Start channels loading thread
            self.channels_thread = XtreamPlayerThread(
                server_url=self.server_url,
                username=self.username,
                password=self.password,
                operation="channels",
                category_type=category_type,
                category_id=category_id
            )

            # Connect signals
            self.channels_thread.channels_loaded.connect(self.on_channels_loaded)
            self.channels_thread.progress_updated.connect(self.progress_bar.setValue)
            self.channels_thread.status_updated.connect(self.status_label.setText)
            self.channels_thread.error_occurred.connect(self.on_connection_error)
            self.channels_thread.finished.connect(self.on_connection_finished)

            # Start the thread
            self.channels_thread.start()

        except Exception as e:
            logger.error(f"Error loading channels: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load channels: {str(e)}")

    def on_channels_loaded(self, channels):
        """Handle channels loaded"""
        try:
            self.current_channels = channels

            # Clear current list and populate with channels
            current_tab = self.content_tabs.currentIndex()
            if current_tab == 0:  # Live TV
                current_list = self.live_list
                icon = "📺"
            elif current_tab == 1:  # Movies
                current_list = self.movies_list
                icon = "🎬"
            else:  # Series
                current_list = self.series_list
                icon = "📺"

            current_list.clear()

            # Add back button
            back_item = QListWidgetItem("⬅️ Back to Categories")
            back_item.setData(Qt.ItemDataRole.UserRole, {"type": "back"})
            current_list.addItem(back_item)

            # Add channels
            for channel in channels:
                channel_name = channel.get('name', 'Unknown')
                item = QListWidgetItem(f"{icon} {channel_name}")
                item.setData(Qt.ItemDataRole.UserRole, channel)
                current_list.addItem(item)

            self.status_label.setText(f"✅ Loaded {len(channels)} items")

        except Exception as e:
            logger.error(f"Error handling channels loaded: {e}")
            self.status_label.setText("Error loading channels")

    def play_video(self):
        """Play or resume video"""
        try:
            if self.vlc_media_player and self.vlc_media_player.get_media():
                # Check current state
                state = self.vlc_media_player.get_state()

                if state == vlc.State.Paused:
                    # Resume from pause
                    self.vlc_media_player.set_pause(0)  # 0 = resume, 1 = pause
                    logger.info("Xtream VLC player resumed from pause")
                else:
                    # Start playing
                    self.vlc_media_player.play()
                    logger.info("Xtream VLC player started playing")

                self.play_btn.setEnabled(False)
                self.pause_btn.setEnabled(True)
                self.stop_btn.setEnabled(True)
                self.status_label.setText("▶️ Playing...")
            else:
                QMessageBox.information(self, "Info", "No content selected. Double-click on a channel to play.")

        except Exception as e:
            logger.error(f"Error playing video: {e}")
            QMessageBox.critical(self, "Error", f"Failed to play video: {str(e)}")

    def pause_video(self):
        """Pause video properly"""
        try:
            if self.vlc_media_player:
                try:
                    # Check current state
                    state = self.vlc_media_player.get_state()

                    if state == vlc.State.Playing:
                        # Pause the video
                        self.vlc_media_player.set_pause(1)  # 1 = pause
                        self.play_btn.setEnabled(True)
                        self.pause_btn.setEnabled(False)
                        self.status_label.setText("⏸️ Paused")
                        logger.info("Xtream VLC player paused successfully")
                    else:
                        logger.warning(f"Cannot pause - current state: {state}")
                        self.status_label.setText("⚠️ Cannot pause - not playing")

                except Exception as vlc_error:
                    logger.warning(f"Error pausing Xtream VLC: {vlc_error}")
                    self.status_label.setText("⚠️ Pause may not work with this stream")

        except Exception as e:
            logger.error(f"Error pausing video: {e}")
            self.status_label.setText("❌ Pause error occurred")

    def stop_video(self):
        """Stop video (completely non-blocking)"""
        try:
            # Update UI immediately to prevent freezing
            self.status_label.setText("⏹️ Stopping...")
            self.play_btn.setEnabled(False)
            self.pause_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)

            # Stop VLC in a separate thread to prevent freezing
            from PyQt6.QtCore import QThread, pyqtSignal

            class StopThread(QThread):
                finished_signal = pyqtSignal()

                def __init__(self, vlc_player):
                    super().__init__()
                    self.vlc_player = vlc_player

                def run(self):
                    try:
                        if self.vlc_player:
                            self.vlc_player.stop()
                            logger.info("Xtream VLC player stopped successfully")
                    except Exception as e:
                        logger.warning(f"Error stopping Xtream VLC: {e}")
                    finally:
                        self.finished_signal.emit()

            # Create and start stop thread
            if hasattr(self, 'vlc_media_player') and self.vlc_media_player:
                self.stop_thread = StopThread(self.vlc_media_player)
                self.stop_thread.finished_signal.connect(self.on_stop_finished)
                self.stop_thread.start()
            else:
                # No VLC player, just update UI
                self.on_stop_finished()

        except Exception as e:
            logger.error(f"Error stopping video: {e}")
            self.on_stop_finished()

    def on_stop_finished(self):
        """Called when stop operation is finished"""
        try:
            # Reset player widget style
            self.vlc_widget.setStyleSheet(f"""
                QWidget {{
                    background-color: #1a1a1a;
                    border: 2px solid {Colors.PRIMARY};
                    border-radius: 8px;
                    min-height: 300px;
                }}
            """)

            # Update UI to final state
            self.status_label.setText("⏹️ Stopped")
            self.play_btn.setEnabled(False)
            self.pause_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)

        except Exception as e:
            logger.error(f"Error in stop finished: {e}")
            self.status_label.setText("❌ Stop error occurred")
            self.play_btn.setEnabled(False)
            self.pause_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)

    def play_channel(self, channel_data):
        """Play a channel (same logic as Xtream Player.py)"""
        try:
            stream_url = channel_data.get("url")
            channel_name = channel_data.get("name", "Unknown")

            if not stream_url:
                QMessageBox.warning(self, "Error", "Stream URL not found")
                return

            # Try integrated VLC player first
            if self.vlc_media_player:
                try:
                    # Create media and play in embedded player
                    media = self.vlc_instance.media_new(stream_url)
                    self.vlc_media_player.set_media(media)
                    self.vlc_media_player.play()

                    self.play_btn.setEnabled(False)
                    self.pause_btn.setEnabled(True)
                    self.stop_btn.setEnabled(True)

                    # Update player widget background to show it's active
                    self.vlc_widget.setStyleSheet(f"""
                        QWidget {{
                            background-color: #1a1a1a;
                            border: 2px solid {Colors.SUCCESS};
                            border-radius: 8px;
                            min-height: 300px;
                        }}
                    """)

                    self.status_label.setText(f"🎬 Playing: {channel_name}")

                    QMessageBox.information(
                        self,
                        "Playing",
                        f"📺 Now playing: {channel_name}\n\n"
                        f"Stream URL: {stream_url[:50]}...\n\n"
                        f"Use the controls below to manage playback."
                    )

                    return

                except Exception as vlc_error:
                    logger.warning(f"Integrated VLC failed: {vlc_error}")

            # Fallback: Show stream URL for external player
            QMessageBox.information(
                self,
                "Stream URL",
                f"📺 {channel_name}\n\n"
                f"Stream URL:\n{stream_url}\n\n"
                f"Copy this URL to your preferred media player."
            )

        except Exception as e:
            logger.error(f"Error playing channel: {e}")
            QMessageBox.critical(self, "Error", f"Failed to play channel: {str(e)}")

    def on_connection_error(self, error_message):
        """Handle connection errors"""
        logger.error(f"Connection error: {error_message}")
        QMessageBox.critical(self, "Connection Error", error_message)
        self.status_label.setText("Connection failed")

    def on_connection_finished(self):
        """Handle connection finished"""
        self.connect_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
